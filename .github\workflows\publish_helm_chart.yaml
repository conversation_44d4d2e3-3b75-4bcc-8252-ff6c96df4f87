name: Publish Opik Helm Chart
run-name: "Publish Opik Helm Chart ${{ github.ref_name }} by @${{ github.actor }}"

on:
  workflow_dispatch:
    inputs:
      version:
        type: string
        required: true
        description: Version
        default: ""
  workflow_call:
    inputs:
      version:
        type: string
        required: true
        description: Version
        default: ""


jobs:

  publish-helm-chart:

    runs-on: ubuntu-latest
    steps:

        - name: Checkout
          uses: actions/checkout@v4.1.1
          with:
            ref: ${{inputs.version}}
            fetch-tags: true
            path: 'src'
            fetch-depth: 0

        - name: Checkout GH Pages branch
          uses: actions/checkout@v4.1.1
          with:
            path: 'dest'
            ref: 'gh-pages'
            fetch-depth: 0

        - name: Install Helm
          uses: azure/setup-helm@v4.2.0
          with:
            version: v3.13.0

        - name: Run lint on Helm chart
          shell: bash
          working-directory: src
          run: |
            set -e
            cd deployment/helm_chart/opik
            helm repo add bitnami https://charts.bitnami.com/bitnami
            helm repo add clickhouse-operator https://docs.altinity.com/clickhouse-operator
            helm dependency build
            helm lint --values values.yaml .
            cd -

        - name: Package helm chart
          shell: bash
          working-directory: src
          run: |
            cd deployment/helm_chart
            helm package --version ${{inputs.version}} --app-version ${{inputs.version}} opik/ -u -d ../../../dest
            cd -
            echo "Copy updated README"
            cp deployment/helm_chart/opik/README.md ../dest/.

        - name: Push New Files
          shell: bash
          working-directory: dest
          run: |
              helm repo index . --url https://raw.githubusercontent.com/comet-ml/opik/gh-pages/
              git config user.name "Helm Updater"
              git config user.email "<EMAIL>"
              git add $(git ls-files -o --exclude-standard)
              git add index.yaml README.md
              git commit -m "Release Opik helm chart ${{inputs.version}}"
              git push   

        - name: Summary
          run: |
            echo "Helm chart is published, version is : ${{inputs.version}}" >> $GITHUB_STEP_SUMMARY


