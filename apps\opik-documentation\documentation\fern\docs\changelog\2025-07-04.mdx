## 🛠 Agent Optimizer 1.0 released!
The Opik Agent Optimizer now supports full agentic systems and not just single prompts. 

With support for LangGraph, Google ADK, PydanticAI, and more, this release brings a simplified API, model customization for evaluation, and standardized interfaces to streamline optimization workflows. [Learn more in the docs.](https://www.comet.com/docs/opik/agent_optimization/overview)

## 🧵 Thread-level improvements
Added **Thread-Level Feedback, Tags & Comments**: You can now add expert feedback scores directly at the thread level, enabling SMEs to review full agent conversations, flag risks, and collaborate with dev teams more effectively. Added support for thread-level tags and comments to streamline workflows and improve context sharing.

<Frame>
    <img src="/img/changelog/2025-07-04/Thread-level_human_feedback.png" />
</Frame>

## 🖥️ UX improvements
- We’ve redesigned the **Opik Home Page** to deliver a cleaner, more intuitive first-use experience, with a focused value proposition, direct access to key metrics, and a polished look. The demo data has also been upgraded to showcase Opik’s capabilities more effectively for new users. Additionally, we've added **inter-project comparison capabilities** for metrics and cost control, allowing you to benchmark and monitor performance and expenses across multiple projects.

<Frame>
    <img src="/img/changelog/2025-07-04/Home_Page_1.png" />
</Frame>

<Frame>
    <img src="/img/changelog/2025-07-04/Home_Page_2.png" />
</Frame>

- **Improved Error Visualization**: Enhanced how span-level errors are surfaced across the project. Errors now bubble up to the project view, with quick-access shortcuts to detailed error logs and variation stats for better debugging and error tracking.

- **Improved Sidebar Hotkeys**: Updated sidebar hotkeys for more efficient keyboard navigation between items and detail views.

## 🔌 SDK, integrations and docs
- Added **Langchain** support in metric classes, allowing use of Langchain as a model proxy alongside LiteLLM for flexible LLM judge customization.
- Added support for the **Gemini 2.5** model family.
- Updated pretty mode to support **Dify** and **LangGraph + OpenAI** responses.
- Added the **OpenAI agents integration cookbook** ([link](https://www.comet.com/docs/opik/cookbook/openai-agents)).
- Added a cookbook on how to import **Huggingface Datasets to Opik** 


👉 [See full commit log on GitHub](https://github.com/comet-ml/opik/compare/1.7.37...1.7.42)

_Releases_: `1.7.37`, `1.7.38`, `1.7.39`, `1.7.40`, `1.7.41`, `1.7.42`
