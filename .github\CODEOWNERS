# This is a comment.
# Each line is a file pattern followed by one or more owners.

# These owners will be the default owners for everything in
# the repo. Unless a later match takes precedence,
# @comet-ml/comet-opik-devs will be requested for
# review when someone opens a pull request.
* @comet-ml/comet-opik-devs # This is an inline comment.

*.md @comet-ml/product @comet-ml/comet-opik-devs
*.mdx @comet-ml/product @comet-ml/comet-opik-devs

/.github/ISSUE_TEMPLATE/ @comet-ml/product @comet-ml/comet-opik-devs
/.github/workflows/ @comet-ml/comet-devops @comet-ml/comet-qa @comet-ml/comet-opik-devs

/apps/opik-documentation/ @comet-ml/product @comet-ml/comet-opik-devs

/deployment/ @comet-ml/comet-devops @comet-ml/deployment-team @comet-ml/comet-opik-devs

/sdks/opik_optimizer @vincentkoc @dsblank @comet-ml/comet-opik-devs

/tests_end_to_end/ @comet-ml/comet-qa @comet-ml/comet-opik-devs
