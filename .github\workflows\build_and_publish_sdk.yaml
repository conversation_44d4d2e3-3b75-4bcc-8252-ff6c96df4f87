name: "Build and Publish SDK"
run-name: "Build SDK ${{ github.ref_name }} by @${{ github.actor }}"

on:
    workflow_dispatch:
      inputs:
        version:
          type: string
          required: true
          description: Version
          default: ""
        is_release:
          type: boolean
          required: false
          default: false
    workflow_call:
      inputs:
        version:
          type: string
          required: true
          description: Version
          default: ""
        is_release:
          type: boolean
          required: false
          default: false

    push:
        branches: 
        - 'main'
        paths: 
          - "sdks/python/**"
        
jobs:
    build-and-publish:
        runs-on: ubuntu-latest
        steps:
        - name: Setup
          id: setup
          run: |
            if [[ "${{inputs.version}}" == ""  ]]; then
              echo "build_from=${{github.ref_name}}" | tee -a $GITHUB_OUTPUT
            else
              echo "build_from=${{inputs.version}}" | tee -a $GITHUB_OUTPUT
            fi
        - name: Checkout
          uses: actions/checkout@v4.1.1
          with:
            ref: ${{steps.setup.outputs.build_from}}
            fetch-tags: true

        - name: Set up Python 3.9
          uses: actions/setup-python@v3
          with:
                python-version: 3.9

        - name: Build pip package
          run: |
                cd sdks/python
                pip3 install -U pip build
                if [[ "${{inputs.version}}" != "" ]]; then export VERSION=${{inputs.version}};fi
                python3 -m build --sdist --wheel --outdir dist/ .
                
        - name: Publish package distributions to PyPI
          if: inputs.is_release
          uses: pypa/gh-action-pypi-publish@v1.12.4
          with:
            password: ${{ secrets.PYPI_API_TOKEN }}
            packages-dir: sdks/python/dist
