## ✨ New Features

- **Opik Agent Optimizer**: A comprehensive toolkit designed to enhance the performance and efficiency of your Large Language Model (LLM) applications. [Read more](https://www.comet.com/docs/opik/agent_optimization/overview)

- **Opik Guardrails**: Guardrails help you protect your application from risks inherent in LLMs. Use them to check the inputs and outputs of your LLM calls, and detect issues like off-topic answers or leaking sensitive information. [Read more](https://www.comet.com/docs/opik/production/guardrails)

## 💡 Product Enhancements

- **New Prompt Selector in Playground** — Choose existing prompts from your Prompt Library to streamline your testing workflows.
- **Improved “Pretty Format” for Agents** — Enhanced readability for complex threads in the UI.

## 🔌 Integrations

- **Vertex AI (Gemini)** — Offline and online evaluation support integrated directly into Opik. Also available now in the Playground.
- **OpenAI Integration in the JS/TS SDK**
- **AWS Strands Agents**
- **Agno Framework**
- **Google ADK Multi-agent support**

## 🛠 SDKs and API Enhancements

- **OpenAI LLM advanced configurations** — Support for custom headers and base URLs.
- **Span Timing Precision** — Time resolution improved to microseconds for accurate monitoring.
- **Better Error Messaging** — More descriptive errors for SDK validation and runtime failures.
- **Stream-based Tracing and Enhanced Streaming support**

👉 [See full commit log on GitHub](https://github.com/comet-ml/opik/compare/1.7.18...1.7.26)

_Releases_: `1.7.19`, `1.7.20`, `1.7.21`, `1.7.22`, `1.7.23`, `1.7.24`, `1.7.25`, `1.7.26`