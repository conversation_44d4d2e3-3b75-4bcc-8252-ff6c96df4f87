name: Front<PERSON>
run-name: "Frontend Linter ${{ github.ref_name }} by @${{ github.actor }}"
on:
  pull_request:
    paths: 
      - "apps/opik-frontend/src/**"
  push:
    branches: 
      - 'main'
    paths: 
      - "apps/opik-frontend/src/**"
  workflow_dispatch:
jobs:
  lint:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: apps/opik-frontend
    steps:
      - name: Checkout code
        uses: actions/checkout@v4.1.1

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - name: Install dependencies
        run: npm install

      - name: ESLint
        run: npm run lint

      - name: Type check
        run: npm run typecheck
