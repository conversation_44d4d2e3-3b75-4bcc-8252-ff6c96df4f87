name: Lint Opik Helm Chart
run-name: "Lint Opik Helm Chart ${{ github.ref_name }} by @${{ github.actor }}"

on:
  workflow_dispatch:
  pull_request:
    paths: 
      - "deployment/helm_chart/opik/**"
  push:
    branches: 
      - 'main'
    paths: 
      - "deployment/helm_chart/opik/**"

jobs:
  
  lint-helm-chart:
    runs-on: ubuntu-latest

    steps:
        - name: Checkout
          uses: actions/checkout@v4.1.1

        - name: Run lint on Helm chart
          run: |
              set -e
              cd deployment/helm_chart/opik
              helm repo add bitnami https://charts.bitnami.com/bitnami
              helm repo add clickhouse-operator https://docs.altinity.com/clickhouse-operator
              helm dependency build
              helm lint --values values.yaml .
              cd -
