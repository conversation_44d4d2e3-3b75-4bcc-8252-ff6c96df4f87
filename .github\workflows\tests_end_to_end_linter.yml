---
    name: E2<PERSON> Tests Linter
    run-name: "E2E Tests Linter ${{ github.ref_name }} by @${{ github.actor }}"
    on:
      pull_request:
        paths:
        - 'tests_end_to_end/**'
      push:
        branches: 
          - 'main'
        paths:
          - 'tests_end_to_end/**'
    jobs:
      lint:
        runs-on: ubuntu-latest  
        defaults:
          run:
            working-directory: tests_end_to_end
        steps:
          - uses: actions/checkout@v4
          - name: install pre-commit
            run: pip install pre-commit
          - name: linting
            run: pre-commit run --all-files
