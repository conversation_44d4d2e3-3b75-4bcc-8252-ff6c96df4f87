name: TypeScript SDK Linter
run-name: "TypeScript SDK Linter ${{ github.ref_name }} by @${{ github.actor }}"
on:
  pull_request:
    paths:
      - "sdks/typescript/**"
  push:
    branches:
      - "main"
    paths:
      - "sdks/typescript/**"
  workflow_dispatch:
jobs:
  lint:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: sdks/typescript
    steps:
      - name: Checkout code
        uses: actions/checkout@v4.2.2

      - name: Set up Node.js
        uses: actions/setup-node@v4.2.0
        with:
          node-version: "20"

      - name: Install dependencies
        run: npm install

      - name: ESLint
        run: npm run lint

      - name: Type check
        run: npm run typecheck
