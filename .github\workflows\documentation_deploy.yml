name: Docs - Publish

on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - 'apps/opik-documentation/documentation/**'

jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Fetch all history for git diff

      - name: Install Fern
        run: npm install -g fern-api@0.64.26

      - name: Publish Docs
        env:
          FERN_TOKEN: ${{ secrets.FERN_TOKEN }}
        run: |
          cd apps/opik-documentation/documentation
          fern generate --docs
