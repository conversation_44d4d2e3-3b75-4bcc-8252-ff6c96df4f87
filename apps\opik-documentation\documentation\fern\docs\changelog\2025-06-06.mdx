## 💡 Product Enhancements
- Ability to upload **CSV datasets** directly through the user interface
- Add **experiment cost tracking** to the Experiments table
- Add hinters and helpers for **onboarding new users** across the platform
- Added "LLM calls count" to the traces table
- Pretty formatting for complex agentic threads
- Preview **support for MP3** files in the frontend

## 🛠 SDKs and API Enhancements
- Good news for JS developers! We've released **experiments support for the JS SDK** (official docs coming very soon)
- New Experiments Bulk API: a new API has been introduced for logging Experiments in bulk.
- Rate Limiting improvements both in the API and the SDK

## 🔌 Integrations
- Support for OpenAI o3-mini and Groq models added to the Playground
- OpenAI Agents: context awareness implemented and robustness improved. Improve thread handling
- Google ADK: added support for multi-agent integration
- LiteLLM: token and cost tracking added for SDK calls. Integration now compatible with opik.configure(...)
	
👉 [See full commit log on GitHub](https://github.com/comet-ml/opik/compare/1.7.26...1.7.31)

_Releases_: `1.7.27`, `1.7.28`, `1.7.29`, `1.7.30`, `1.7.31`