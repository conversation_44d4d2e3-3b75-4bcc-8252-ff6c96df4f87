# Workflow to run GenAI tests
#
# Please read inputs to provide correct values.
#
name: SDK Lib GenAI (Google) Tests
run-name: "SDK Lib GenAI Tests ${{ github.ref_name }} by @${{ github.actor }}"
env:
  GCP_CREDENTIALS_JSON: ${{ secrets.GCP_CREDENTIALS_JSON }}
  GOOGLE_CLOUD_LOCATION: us-east1
  GOOGLE_CLOUD_PROJECT: opik-sdk-tests
  OPIK_ENABLE_LITELLM_MODELS_MONITORING: False
  OPIK_SENTRY_ENABLE: False
on:
  workflow_call:

jobs:
  tests:
    name: GenAI Python ${{matrix.python_version}}
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: sdks/python

    strategy:
      fail-fast: true
      matrix:
        python_version: [
          # "3.9", skipped because asyncio requires async context in 3.9 to create a client
          "3.10",
          "3.11",
          "3.12"
        ]

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Setup Python ${{matrix.python_version}}
        uses: actions/setup-python@v5
        with:
          python-version: ${{matrix.python_version}}

      - name: Install opik
        run: pip install .

      - name: Install test tools
        run: |
          cd ./tests
          pip install --no-cache-dir --disable-pip-version-check -r test_requirements.txt

      - name: Install lib
        run: |
          cd ./tests
          pip install --no-cache-dir --disable-pip-version-check -r library_integration/genai/requirements.txt

      - name: Run tests
        run: |
          cd ./tests/library_integration/genai/
          python -m pytest  -vv .