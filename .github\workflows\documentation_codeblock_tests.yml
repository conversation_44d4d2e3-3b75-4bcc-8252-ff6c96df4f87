name: Docs - Test codeblocks
on:
  workflow_dispatch:
  # pull_request:
  #   paths:
  #     - 'apps/opik-documentation/documentation/fern/docs/*.md'
  #     - 'apps/opik-documentation/documentation/fern/docs/*.mdx'
  #     - 'apps/opik-documentation/documentation/fern/docs/**/*.md'
  #     - 'apps/opik-documentation/documentation/fern/docs/**/*.mdx'

jobs:
  collect_test_paths:
    runs-on: ubuntu-latest
    outputs:
      test_paths: ${{ steps.paths.outputs.paths }}
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Fetch all history for git diff

      - id: paths
        working-directory: apps/opik-documentation/documentation
        run: |
          # Get list of changed files in docs directory
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            # For pull requests, compare with base branch
            CHANGED_FILES=$(git diff --name-only origin/${{ github.base_ref }} | grep -E '^apps/opik-documentation/documentation/fern/docs/.*\.(md|mdx)$' || true)
            if [ -n "$CHANGED_FILES" ]; then
              echo "paths=$(echo "$CHANGED_FILES" | sed 's|apps/opik-documentation/documentation/||' | jq -R -s 'split("\n")[:-1]' -c)" >> $GITHUB_OUTPUT
            else
              echo "paths=[]" >> $GITHUB_OUTPUT
            fi
          else
            # For manual runs, get all md/mdx files
            FILES=$(find fern/docs -type f \( -name "*.md" -o -name "*.mdx" \))
            if [ -n "$FILES" ]; then
              echo "paths=$(echo "$FILES" | jq -R -s 'split("\n")[:-1]' -c)" >> $GITHUB_OUTPUT
            else
              echo "paths=[]" >> $GITHUB_OUTPUT
            fi
          fi

  test:
    needs: collect_test_paths
    runs-on: ubuntu-latest
    timeout-minutes: 15
    env:
      OPENAI_API_KEY: ${{ secrets.DOCS_OPENAI_API_KEY }}
      ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
      OPIK_WORKSPACE: ${{ secrets.COMET_WORKSPACE }}
      OPIK_API_KEY: ${{ secrets.COMET_API_KEY }}
      GUARDRAILS_API_KEY: ${{ secrets.GUARDRAILS_API_KEY }}
      OPIK_SENTRY_ENABLE: False
    strategy:
      matrix:
        path: ${{ fromJson(needs.collect_test_paths.outputs.test_paths) }}
      fail-fast: false
    steps:
      - uses: actions/checkout@v3
        if: github.event_name == 'pull_request'
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0

      - uses: actions/checkout@v3
        if: github.event_name != 'pull_request'
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install dependencies
        working-directory: apps/opik-documentation/documentation
        run: |
          python -m pip install --upgrade pip
          pip install pytest
          pip install -r requirements.txt
          
      - name: Run tests
        working-directory: apps/opik-documentation/documentation
        env:
          PYTHONPATH: ${{ github.workspace }}/sdks/opik_optimizer/src:${{ github.workspace }}/sdks/python/src
        run: |
          if [ -n "${{ matrix.path }}" ]; then
            pytest ${{ matrix.path }} -v --suppress-no-test-exit-code --default-package=../../../sdks/python
          fi
