# Internal release
name: "Release"
run-name: "Release from ${{github.ref_name}} by @${{ github.actor }}"

on:
  workflow_dispatch:
  workflow_call: #  in order to make this action been called from outside
    outputs:
      version:
        description: 'The release version'
        value: ${{ jobs.set-version.outputs.version }}

jobs:
  set-version:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4.1.1

      - name: Set version
        id: version
        run: |
          echo "version=$(cat version.txt)" | tee -a $GITHUB_OUTPUT

  create-git-tag:
    needs:
      - set-version
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4.1.1

      - name: Create git tag
        run: |
          set -x
          git config --local user.email "<EMAIL>"
          git config --local user.name "github-actions"
          git tag ${{needs.set-version.outputs.version}}
          git push --no-verify origin ${{needs.set-version.outputs.version}}

  release-apps:
    needs:
      - set-version
      - create-git-tag
    uses: ./.github/workflows/build_apps.yml
    secrets: inherit
    with:
      version: ${{needs.set-version.outputs.version}}
      is_release: true

  release-sdk:
    needs:
      - set-version
      - create-git-tag
    uses: ./.github/workflows/build_and_publish_sdk.yaml
    secrets: inherit
    with:
      version: ${{needs.set-version.outputs.version}}
      is_release: true

  release-typescript-sdk:
    needs:
      - set-version
      - create-git-tag
    uses: ./.github/workflows/typescript_sdk_publish.yml
    secrets: inherit
    with:
      version: ${{needs.set-version.outputs.version}}
      is_release: true

  publish-helm-chart:
    needs:
      - set-version
      - create-git-tag
    uses: ./.github/workflows/publish_helm_chart.yaml
    secrets: inherit
    with:
      version: ${{needs.set-version.outputs.version}}

  update_version_txt:
    needs:
      - set-version
      - create-git-tag
      - release-apps
      - release-sdk
      - publish-helm-chart
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4.1.1
        with:
          ref: main
          fetch-depth: 0
          token: ${{ secrets.GH_PAT_TO_ACCESS_GITHUB_API }}

      - name: Bump version on main branch
        id: update_version
        shell: bash
        run: |
          set -x
          BASE_VERSION=$(tr -d '\r' < version.txt | xargs)
          IFS='.' read -r -a version_parts <<< "$BASE_VERSION"
          # Ensure all version parts are set
          for i in {0..2}; do
            version_parts[$i]=${version_parts[$i]:-0}
          done
          # Convert to decimal before incrementing to avoid octal interpretation issues
          version_parts[2]=$((10#${version_parts[2]} + 1))
          new_version="${version_parts[0]}.${version_parts[1]}.${version_parts[2]}"

          # Update version file with the new version
          echo "$new_version" > version.txt
          echo "new_version=${new_version}" >> $GITHUB_OUTPUT

      - name: Commit updated version file
        shell: bash
        run: |

          git config --local user.email "<EMAIL>"
          git config --local user.name "github-actions"
          git commit -a -m "Update base version to ${{steps.update_version.outputs.new_version}}"
          echo "Version updated to ${{steps.update_version.outputs.new_version}} on main" >> $GITHUB_STEP_SUMMARY

      - name: Push changes
        uses: ad-m/github-push-action@master
        with:
          github_token: ${{ secrets.GH_PAT_TO_ACCESS_GITHUB_API }}
          branch: ${{ github.ref }}
          force_with_lease: true

  create-github-release:
    needs:
      - set-version
      - create-git-tag
    runs-on: ubuntu-latest
    steps:
      - name: Create GitHub release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{needs.set-version.outputs.version}}
          generate_release_notes: true

      - name: Delete Release Draft
        uses: hugo19941994/delete-draft-releases@v1.0.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
