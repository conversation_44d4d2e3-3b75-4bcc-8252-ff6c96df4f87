name: Installation Tests

on:
  schedule:
    - cron: '0 12 * * *' # runs every day at noon UTC
  workflow_dispatch:

jobs:
  test-opiksh:
    name: opik.sh (main README)
    runs-on: ubuntu-24.04
    timeout-minutes: 15
    strategy:
      fail-fast: false
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.12

      - name: Install Python dependencies
        run: |
          pip install -r ${{ github.workspace }}/tests_end_to_end/test_requirements.txt
          pip install opik
          playwright install chromium

      - name: Install using opik.sh
        env:
          OPIK_USAGE_REPORT_ENABLED: false
        run: |
          cd ${{ github.workspace }}
          ./opik.sh

      - name: Run trace test
        run: |
          cd ${{ github.workspace }}/tests_end_to_end
          export PYTHONPATH='.'
          pytest -xvs tests/Installation/test_trace_logging.py --browser chromium

      - name: Stop Opik
        if: always()
        run: |
          cd ${{ github.workspace }}
          ./opik.sh --stop

  test-docker-compose-up:
    name: docker compose up --detach (Comet docs)
    runs-on: ubuntu-24.04
    timeout-minutes: 15
    strategy:
      fail-fast: false
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install Python dependencies
        run: |
          pip install -r ${{ github.workspace }}/tests_end_to_end/test_requirements.txt
          pip install opik
          playwright install chromium

      - name: Install using docker compose up --detach
        env:
          OPIK_USAGE_REPORT_ENABLED: false
        run: |
          cd ${{ github.workspace }}/deployment/docker-compose
          docker compose up --detach

      - name: Check Docker pods are up (Local)
        run: |
          chmod +x ./tests_end_to_end/installer_utils/check_docker_compose_pods.sh
          ./tests_end_to_end/installer_utils/check_docker_compose_pods.sh
        shell: bash

      - name: Check backend health (Local)
        run: |
          chmod +x ./tests_end_to_end/installer_utils/check_backend.sh
          ./tests_end_to_end/installer_utils/check_backend.sh
        shell: bash

      - name: Run trace test
        run: |
          cd ${{ github.workspace }}/tests_end_to_end
          export PYTHONPATH='.'
          pytest -xvs tests/Installation/test_trace_logging.py --browser chromium

      - name: Stop Opik
        if: always()
        run: |
          cd ${{ github.workspace }}/deployment/docker-compose
          docker compose down

  test-docker-compose-build:
    name: docker compose up --build (Comet docs)
    runs-on: ubuntu-24.04
    timeout-minutes: 15
    strategy:
      fail-fast: false
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install Python dependencies
        run: |
          pip install -r ${{ github.workspace }}/tests_end_to_end/test_requirements.txt
          pip install opik
          playwright install chromium

      - name: Install with --build flag
        env:
          OPIK_USAGE_REPORT_ENABLED: false
        run: |
          cd ${{ github.workspace }}/deployment/docker-compose
          docker compose up -d --build
          
      - name: Check Docker pods are up (Local)
        run: |
          chmod +x ./tests_end_to_end/installer_utils/check_docker_compose_pods.sh
          ./tests_end_to_end/installer_utils/check_docker_compose_pods.sh
        shell: bash

      - name: Check backend health (Local)
        run: |
          chmod +x ./tests_end_to_end/installer_utils/check_backend.sh
          ./tests_end_to_end/installer_utils/check_backend.sh
        shell: bash

      - name: Run trace test
        run: |
          cd ${{ github.workspace }}/tests_end_to_end
          export PYTHONPATH='.'
          pytest -xvs tests/Installation/test_trace_logging.py --browser chromium

      - name: Stop Opik
        if: always()
        run: |
          cd ${{ github.workspace }}/deployment/docker-compose
          docker compose down

  test-docker-compose-with-version:
    name: Set version - docker compose (deployment README)
    runs-on: ubuntu-24.04
    timeout-minutes: 15
    strategy:
      fail-fast: false
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install Python dependencies
        run: |
          pip install -r ${{ github.workspace }}/tests_end_to_end/test_requirements.txt
          pip install opik
          playwright install chromium

      - name: Install with docker compose with specific version set
        env:
          OPIK_USAGE_REPORT_ENABLED: false
        run: |
          cd ${{ github.workspace }}/deployment/docker-compose
          export OPIK_VERSION=1.6.5
          docker compose -f docker-compose.yaml up -d
          
      - name: Check Docker pods are up (Local)
        run: |
          chmod +x ./tests_end_to_end/installer_utils/check_docker_compose_pods.sh
          ./tests_end_to_end/installer_utils/check_docker_compose_pods.sh
        shell: bash

      - name: Check backend health (Local)
        run: |
          chmod +x ./tests_end_to_end/installer_utils/check_backend.sh
          ./tests_end_to_end/installer_utils/check_backend.sh
        shell: bash

      - name: Run trace test
        run: |
          cd ${{ github.workspace }}/tests_end_to_end
          export PYTHONPATH='.'
          pytest -xvs tests/Installation/test_trace_logging.py --browser chromium

      - name: Stop Opik
        if: always()
        run: |
          cd ${{ github.workspace }}/deployment/docker-compose
          docker compose down

  test-docker-compose-with-pull:
    name: Docker compose pull + up (Deployment README)
    runs-on: ubuntu-24.04
    timeout-minutes: 15
    strategy:
      fail-fast: false
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install Python dependencies
        run: |
          pip install -r ${{ github.workspace }}/tests_end_to_end/test_requirements.txt
          pip install opik
          playwright install chromium

      - name: Install with docker compose after pulling latest version
        env:
          OPIK_USAGE_REPORT_ENABLED: false
        run: |
          cd ${{ github.workspace }}/deployment/docker-compose
          docker compose pull
          docker compose -f docker-compose.yaml up -d
          
      - name: Check Docker pods are up (Local)
        run: |
          chmod +x ./tests_end_to_end/installer_utils/check_docker_compose_pods.sh
          ./tests_end_to_end/installer_utils/check_docker_compose_pods.sh
        shell: bash

      - name: Check backend health (Local)
        run: |
          chmod +x ./tests_end_to_end/installer_utils/check_backend.sh
          ./tests_end_to_end/installer_utils/check_backend.sh
        shell: bash

      - name: Run trace test
        run: |
          cd ${{ github.workspace }}/tests_end_to_end
          export PYTHONPATH='.'
          pytest -xvs tests/Installation/test_trace_logging.py --browser chromium

      - name: Stop Opik
        if: always()
        run: |
          cd ${{ github.workspace }}/deployment/docker-compose
          docker compose down
