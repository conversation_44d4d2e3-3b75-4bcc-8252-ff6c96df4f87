---
name: <PERSON><PERSON>
run-name: "SDK Linter ${{ github.ref_name }} by @${{ github.actor }}"
on:
  pull_request:
    paths:
    - 'sdks/python/**'
  push:
    branches: 
      - 'main'
    paths:
      - 'sdks/python/**'
jobs:
  lint:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: sdks/python
    steps:
      - uses: actions/checkout@v4
      - name: install pre-commit
        run: pip install pre-commit
      - name: linting
        run: pre-commit run --all-files --show-diff-on-failure
