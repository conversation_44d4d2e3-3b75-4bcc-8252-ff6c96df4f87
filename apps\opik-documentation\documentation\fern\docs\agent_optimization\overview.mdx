---
title: "Opik Agent Optimization Overview"
subtitle: "Automate enhancing LLM prompts and agent performance."
description: "Learn about Opik's automated LLM prompt and agent optimization SDK. Discover MetaPrompt, Few-shot Bayesian, MIPRO, and Evolutionary optimization algorithms for enhanced performance."
keywords: "LLM optimization, prompt engineering, agent optimization, MetaPrompt, Few-shot learning, Bayesian optimization, MIPRO, Evolutionary algorithms, DSPy"
og:image: "/img/agent_optimization/introducing.png"
---

<Warning>
  Opik Agent Optimizer, including the optimizers described are currently in **Public Beta**. We are actively working on
  improving these features and welcome your [feedback on GitHub](https://github.com/comet-ml/opik/issues)!
</Warning>

<Frame>
  <img src="/img/agent_optimization/introducing.png" alt="Introducing Opik Agent Optimizer" />
</Frame>

**Opik Agent Optimizer** is a comprehensive toolkit designed to enhance the performance and
efficiency of your Large Language Model (LLM) applications. Rather than manually editing prompts
and running evaluation, you can use Opik Agent Optimizer to automatically optimize your prompts.

<Frame>
  <img src="/img/agent_optimization/dashboard.png" alt="Opik Agent Optimizer Dashboard" />
</Frame>

## Why optimize prompts ?

Prompt engineering is a skill that can be difficult to master as highlighted by
the [Anthropic](https://docs.anthropic.com/en/docs/build-with-claude/prompt-engineering/overview#how-to-prompt-engineer),
[OpenAI](https://help.openai.com/en/articles/6654000-best-practices-for-prompt-engineering-with-the-openai-api)
and [Google](https://www.kaggle.com/whitepaper-prompt-engineering) prompt engineering guides. There
are a lot of techniques that can be used to help LLMs generate the desired output.

Prompt optimization solves many of the issues that come with prompt engineering:

1. Prompt engineering is not easily repeatable or scalable alone
2. Possible performance degration across models, you need to tune prompts for each model
3. Optimization may unlock performance, cost and reliability improvements
4. As systems evolve, manually tuning multiple prompts becomes increasingly difficult

So when should you use prompt optimization?

| **Aspect**    | **Prompt Engineering**                             | **Prompt Optimization**                           |
| ------------- | -------------------------------------------------- | ------------------------------------------------- |
| **Scope**     | Broad, includes designing, experimenting, refining | Narrow, improving already existing prompts        |
| **Goal**      | Create a working prompt for a specific task        | Maximize performance (accuracy, efficiency, etc.) |
| **Involves**  | Initial drafting, understanding model behavior     | Tweaking wording, structure, or context           |
| **When used** | Early in task setup or experimentation             | After a baseline prompt is in place               |

## Optimizing a prompt

You can optimize any prompt in just a few lines of code

```python
import opik_optimizer
from opik.evaluation.metrics import LevenshteinRatio

# Load a demo dataset
dataset = opik_optimizer.datasets.tiny_test()

# Define a metric
def levenshtein_ratio(dataset_item, llm_output):
    metric = LevenshteinRatio()
    return metric.score(reference=dataset_item['label'], output=llm_output)

# Define the prompt to optimize
prompt = opik_optimizer.ChatPrompt(
  messages=[
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "{text}"},
  ]
)

# Run the optimization
optimizer = opik_optimizer.MetaPromptOptimizer(model="openai/gpt-4")
opt_result = optimizer.optimize_prompt(
  prompt=prompt,
  dataset=dataset,
  metric=levenshtein_ratio
)

print(opt_result.prompt)
```

You can learn more about running your first optimization in the
[Quickstart guide](/agent_optimization/opik_optimizer/quickstart).

## Optimization Algorithms

### Supported algorithms

The [Opik Agent Optimizer](https://pypi.org/project/opik-optimizer/) is an experimental Python
library that aims at implementing Prompt and Agent Optimization algorithms in a consistent format.

<Note>
  All optimizers leverage **LiteLLM** for broad model compatibility. This means you can use models from OpenAI, Azure,
  Anthropic, Google, local Ollama instances, and many more. For details on how to specify different models, see the
  [LiteLLM Support for Optimizers](/agent_optimization/opik_optimizer/litellm_support) guide.
</Note>

The following algorithms have been implemented:

| **Algorithm**                                                                               | **Description**                                                                                                                                                                                                      |
| ------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [MetaPrompt Optimization](/agent_optimization/algorithms/metaprompt_optimizer)              | Uses an LLM ("reasoning model") to critique and iteratively refine an initial instruction prompt. Good for general prompt wording, clarity, and structural improvements.                                             |
| [Few-shot Bayesian Optimization](/agent_optimization/algorithms/fewshot_bayesian_optimizer) | Specifically for chat models, this optimizer uses Bayesian optimization (Optuna) to find the optimal number and combination of few-shot examples (demonstrations) to accompany a system prompt.                      |
| [MIPRO Optimization](/agent_optimization/algorithms/mipro_optimizer)                        | A prompt engineering algorithm that uses a MIPRO (Multi-Instance Prompt Refinement) approach to generate a set of candidate prompts and then uses a Bayesian optimization algorithm to identify the best prompt.     |
| [Evolutionary Optimization](/agent_optimization/algorithms/evolutionary_optimizer)          | Employs genetic algorithms to evolve a population of prompts. Can discover novel prompt structures and supports multi-objective optimization (e.g., score vs. length). Can use LLMs for advanced mutation/crossover. |

<Tip>
  If you would like us to implement another optimization algorithm, reach out to us on
  [Github](https://github.com/comet-ml/opik) or feel free to contribute by [extending
  optimizers](/agent_optimization/advanced/extending_optimizer).
</Tip>

### Benchmark results

<Warning>
  We are currently working on the benchmarking results, these are early preliminary results and are subject to change.
  You can learn more about our benchmarks
  [here](https://github.com/comet-ml/opik/blob/main/sdks/opik_optimizer/benchmarks/run_benchmark.py).
</Warning>

Each optimization algorithm is evaluated against different use-cases and datasets:

1. Arc: The [ai2_arc](https://huggingface.co/datasets/allenai/ai2_arc) dataset contains a set of
   multiple choice science questions
2. GSM8K: The [gsm8k](https://huggingface.co/datasets/openai/gsm8k) dataset contains a set of math problems
3. medhallu: The [medhallu](https://huggingface.co/datasets/UTAustin-AIHealth/MedHallu) dataset
   contains a set of medical questions
4. RagBench: The [ragbench](https://huggingface.co/datasets/wandb/ragbench-sentence-relevance-balanced/discussions)
   dataset contains a set of retrieval (RAG) examples.

| Rank | Algorithm/Optimizer              | Average Score | Arc    | GSM8K  | medhallu | RagBench |
| ---- | -------------------------------- | ------------- | ------ | ------ | -------- | -------- |
| 1    | Few-shot Bayesian Optimization   | 67.33%        | 28.09% | 59.26% | 91.80%   | 90.15%   |
| 2    | Evolutionary Optimization        | 63.13%        | 40.00% | 25.53% | 95.00%   | 92.00%   |
| 3    | Mipro Optimization (w/ no tools) | 55.91%        | 19.70% | 39.70% | 92.70%   | 89.28%   |
| 4    | MetaPrompt Optimization          | 52.01%        | 25.00% | 26.93% | 91.79%   | 64.31%   |
| 5    | No optimization                  | 11.99%        | 1.69%  | 24.06% | 12.38%   | 9.81%    |

The results above are benchmarks tested against `gpt-4o-mini`, we are using various metrics
depending on the dataset including Levenshtein Ratio, Answer Relevance and Hallucination. The
results might change if you use a different model, configuration, dataset and starting prompt(s).
