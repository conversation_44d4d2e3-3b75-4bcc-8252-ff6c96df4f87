# Workflow to run DSPy tests
#
# Please read inputs to provide correct values.
#
name: SDK Lib DSPy Tests
run-name: "SDK Lib DSPy Tests ${{ github.ref_name }} by @${{ github.actor }}"
env:
  OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
  OPENAI_ORG_ID:  ${{ secrets.OPENAI_ORG_ID }}
  OPIK_ENABLE_LITELLM_MODELS_MONITORING: False
  OPIK_SENTRY_ENABLE: False
on:
  workflow_call:

jobs:
  tests:
    name: DSPy Python ${{matrix.python_version}}
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: sdks/python

    strategy:
      fail-fast: true
      matrix:
        python_version: ["3.9", "3.10", "3.11", "3.12"]

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Setup Python ${{matrix.python_version}}
        uses: actions/setup-python@v5
        with:
          python-version: ${{matrix.python_version}}

      - name: Install opik
        run: pip install .

      - name: Install test tools
        run: |
          cd ./tests
          pip install --no-cache-dir --disable-pip-version-check -r test_requirements.txt

      - name: Install lib
        run: |
          cd ./tests
          pip install --no-cache-dir --disable-pip-version-check -r library_integration/dspy/requirements.txt

      - name: Run tests
        run: |
          cd ./tests/library_integration/dspy/
          python -m pytest  -vv .