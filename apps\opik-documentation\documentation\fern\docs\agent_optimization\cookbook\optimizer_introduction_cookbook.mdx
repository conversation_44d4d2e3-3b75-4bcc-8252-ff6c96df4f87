---
title: "Optimizer Introduction Cookbook"
subtitle: "Quick example notebook using HotPotQA dataset"
description: "Learn how to use Opik Agent Optimizer with the HotPotQA dataset through an interactive notebook, covering setup, configuration, and optimization techniques."
---

<Info>
  This example demonstrates end-to-end prompt optimization on the HotPotQA dataset using Opik Agent Optimizer. All
  steps, code, and explanations are provided in the interactive Colab notebook below.
</Info>

## Load Example Notebook

<Note>
  To follow this example, simply open the Colab notebook below. You can run, modify, and experiment with the workflow
  directly in your browser—no local setup required.
</Note>

| Platform                     | Launch Link                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| ---------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **Google Colab (Preferred)** | [<img src="https://colab.research.google.com/assets/colab-badge.svg" alt="Open in Colab" style="vertical-align: middle; display: inline-block;"/>](https://colab.research.google.com/github/comet-ml/opik/blob/main/sdks/opik_optimizer/notebooks/OpikOptimizerIntro.ipynb)                                                                                                                                                                                                                                                                                                                                              |
| **Alternatives**             | [<img src="https://github.com/codespaces/badge.svg" alt="Open in GitHub Codespaces" style="vertical-align: middle; display: inline-block;"/>](https://github.com/codespaces/new?repo=comet-ml/opik&ref=main&machine=basicLinux32gb&devcontainer_path=.devcontainer%2Fopik_optimizer%2Fdevcontainer.json&location=EastUs)<br /><br /> [<img src="https://deepnote.com/buttons/launch-in-deepnote.svg" alt="Open in Deepnote" style="vertical-align: middle; display: inline-block;"/>](https://deepnote.com/launch?url=https://github.com/comet-ml/opik/blob/main/sdks/opik_optimizer/notebooks/OpikOptimizerIntro.ipynb) |

## What you'll learn

- How to set up Opik Agent Optimizer SDK
- How to setup Opik Cloud (Comet Account) for prompt optimization
- How to use the HotPotQA dataset for multi-hop question answering
- How to define metrics and task configs
- How to run the `FewShotBayesianOptimizer` and interpret results
- How to visualize optimization runs in the Opik UI

## Quick Start

1. Click the Colab badge above to launch the notebook.
2. Follow the step-by-step instructions in the notebook.
3. For more details, see the [Opik Agent Optimizer documentation](/agent_optimization/overview).
