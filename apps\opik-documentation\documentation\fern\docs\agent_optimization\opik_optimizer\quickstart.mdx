---
title: "Opik Agent Optimizer: Getting Started"
subtitle: "Simple few step process to getting you optimized"
description: "Get started with Opik Agent Optimizer SDK to improve your LLM prompts through systematic optimization. Learn installation, configuration, and basic usage."
pytest_codeblocks_skip: true
---

This guide will help you get started with Opik Agent Optimizer SDK for improving your LLM prompts
through systematic optimization.

## Prerequisites

- Python 3.9 or higher
- An Opik API key ([sign up here](https://www.comet.com/signup?from=llm) if you don't have one)

## Getting Started with Optimizers

Here's a step-by-step guide to get you up and running with Opik Agent Optimizer:

<Steps>
  <Step title="1. Install Opik and the optimizer package">
    Install the required packages using pip or uv (recommended for faster installation):
    ```bash
    # Using pip
    pip install opik opik-optimizer

    # Using uv (recommended for faster installation)
    uv pip install opik-optimizer
    ```

    Then configure your Opik environment:
    ```bash
    # Install the Opik CLI if not already installed
    pip install opik

    # Configure your API key
    opik configure
    ```

  </Step>
  <Step title="2. Import necessary modules">
    First, we import all the required classes and functions from `opik` and `opik_optimizer`.
    ```python
    from opik.evaluation.metrics import LevenshteinRatio
    from opik_optimizer import MetaPromptOptimizer, ChatPrompt
    from opik_optimizer.datasets import tiny_test
    ```
  </Step>
  <Step title="3. Define your evaluation dataset">
    You can use a demo dataset for testing, or create/load your own. You can learn more about
    creating datasets in the [Manage Datasets](/evaluation/manage_datasets) documentation.
    ```python
    # You can use a demo dataset for testing, or your own dataset
    dataset = tiny_test()
    print(f"Using dataset: {dataset.name}, with {len(dataset.get_items())} items.")
    ```
  </Step>
  <Step title="4. Configure the evaluation metric">
    `metric` tells the optimizer how to score the LLM's outputs. Here, `LevenshteinRatio` measures the similarity between the model's response and a "label" field in the dataset.
    ```python
    # This example uses Levenshtein distance to measure output quality
    def levenshtein_ratio(dataset_item, llm_output):
        metric = LevenshteinRatio()
        return metric.score(reference=dataset_item['label'], output=llm_output)
    ```
  </Step>
  <Step title="5. Define your base prompt">
    This is the initial instruction that the `MetaPromptOptimizer` will try to enhance:
    ```python
    prompt = ChatPrompt(
      project_name="Prompt Optimization Quickstart",
      messages=[
        {"role": "system", "content": "You are an expert assistant. Your task is to answer questions accurately and concisely. Consider the context carefully before responding."},
        {"role": "user", "content": "{text}"}
      ]
    )
    print("Prompt defined.")
    ```
  </Step>
  <Step title="6. Choose and configure an optimizer">
    Instantiate `MetaPromptOptimizer`, specifying the model to be used in the optimization
    process.
    ```python
    optimizer = MetaPromptOptimizer(
        model="gpt-4",
    )
    print(f"Optimizer configured: {type(optimizer).__name__}")
    ```
  </Step>
  <Step title="7. Run the optimization">
    The `optimizer.optimize_prompt(...)` method is called with the dataset, metric configuration,
    and prompt to start the optimization process.
    ```python
    print("Starting optimization...")
    result = optimizer.optimize_prompt(
        prompt=prompt,
        dataset=dataset,
        metric=levenshtein_ratio,
    )
    print("Optimization finished.")
    ```
  </Step>
  <Step title="9. View results in the CLI">
    After optimization completes, call `result.display()` to see a summary of the optimization,
    including the best prompt found and its score, directly in your terminal.

    ```python
    print("Optimization Results:")
    result.display()
    ```

    The `OptimizationResult` object also contains more details in `result.history` and
    `result.details`. The optimization results will be displayed in your console, showing the
    progress and final scores.

    <Frame>
      <img src="/img/agent_optimization/sdk_cli_results.png" alt="Opik agent optimization progress in CLI" />
    </Frame>

  </Step>
  <Step title="10. View results in the Opik dashboard">
    In addition to the CLI output, your optimization results are also available in the Opik Agent
    Optimization dashboard for further analysis and visualization.

    <Frame>
      <img src="/img/agent_optimization/trial_dashboard.png" alt="Opik agent optimization results in dashboard" />
    </Frame>

  </Step>
</Steps>

## Next Steps

1. Explore different [optimization algorithms](/agent_optimization/overview#optimization-algorithms)
   to choose the best one for your use case
2. Understand [prompt engineering best practices](/agent_optimization/best_practices/prompt_engineering)
3. Set up your own [evaluation datasets](/agent_optimization/opik_optimizer/datasets)
4. Review the [API reference](/agent_optimization/opik_optimizer/reference) for detailed
   configuration options

<Info>
  🚀 Want to see Opik Agent Optimizer in action? Check out our [Example Projects &
  Cookbooks](/agent_optimization/opik_optimizer/quickstart) for runnable Colab notebooks covering real-world optimization workflows,
  including HotPotQA and synthetic data generation.
</Info>
