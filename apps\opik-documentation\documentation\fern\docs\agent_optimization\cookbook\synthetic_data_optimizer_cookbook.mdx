---
title: "Synthetic Data Optimizer Cookbook"
subtitle: "Advanced example notebook using syntethic datasets"
description: "Learn how to generate synthetic Q&A data from Opik traces and optimize prompts using the MetaPromptOptimizer through an interactive notebook."
---

<Info>
  This example shows how to generate synthetic Q&A data from Opik traces and optimize prompts using the
  MetaPromptOptimizer. All steps, code, and explanations are provided in the interactive Colab notebook below.
</Info>

## Load Example Notebook

<Note>
  To follow this example, simply open the Colab notebook below. You can run, modify, and experiment with the workflow directly in your browser—no local setup required.
</Note>

| Platform                | Launch Link |
|-------------------------|-------------|
| **Google Colab (Preferred)** | [<img src="https://colab.research.google.com/assets/colab-badge.svg" alt="Open in Colab" style="vertical-align: middle; display: inline-block;"/>](https://colab.research.google.com/github/comet-ml/opik/blob/main/sdks/opik_optimizer/notebooks/OpikSyntethicDataOptimizer.ipynb) |
| **Alternatives**        | [<img src="https://github.com/codespaces/badge.svg" alt="Open in GitHub Codespaces" style="vertical-align: middle; display: inline-block;"/>](https://github.com/codespaces/new?repo=comet-ml/opik&ref=main&machine=basicLinux32gb&devcontainer_path=.devcontainer%2Fopik_optimizer%2Fdevcontainer.json&location=EastUs)<br /><br /> [<img src="https://deepnote.com/buttons/launch-in-deepnote.svg" alt="Open in Deepnote" style="vertical-align: middle; display: inline-block;"/>](https://deepnote.com/launch?url=https://github.com/comet-ml/opik/blob/main/sdks/opik_optimizer/notebooks/OpikSyntethicDataOptimizer.ipynb) |

## What you'll learn

- How to extract and clean traces from Opik
- How to generate synthetic Q&A datasets using [tinyqabenchmarkpp](https://pypi.org/project/tinyqabenchmarkpp/)
- How to upload and manage datasets in Opik
- How to configure and run the MetaPromptOptimizer on synthetic data
- How to interpret and visualize optimization results

## Quick Start

1. Click the Colab badge above to launch the notebook.
2. Follow the notebook's step-by-step workflow.
3. For more details, see the [Opik Agent Optimizer documentation](/agent_optimization/overview).

