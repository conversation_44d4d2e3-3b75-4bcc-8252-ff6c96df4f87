name: Deploy Opik Python SDK docs
run-name: Deploy Python SDK docs ${{ github.ref_name }} to S3 ${{ inputs.environment }} by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: Choose environment
        options:
          - staging
          - production
          - development
jobs:
  upload:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    steps:      
      - name: checkout
        uses: actions/checkout@v4
      
      - name: Build Python SDK docs
        run: |
            echo "Build sdk documentation"
            cd apps/opik-documentation/python-sdk-docs
            pip -V
            pip install -r requirements.txt
            pip install ../../../sdks/python
            make build
      
      - name: Upload sdk docs to S3 ${{ inputs.environment }}
        uses: jakejarvis/s3-sync-action@master
        with:
          args: --follow-symlinks --delete --exclude '.git*'
        env:
          AWS_S3_BUCKET: site.comet.com
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: us-east-1
          DEST_DIR: ${{ inputs.environment }}/docs-opik/python-sdk-reference
          SOURCE_DIR: apps/opik-documentation/python-sdk-docs/build/html
