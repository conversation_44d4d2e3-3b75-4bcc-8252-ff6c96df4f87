# Runner for the suite of library integration tests
#
name: SDK Library Integration Tests Runner
run-name: "SDK Library Integration Tests Runner ${{ github.ref_name }} by @${{ github.actor }}"
permissions:
  contents: read
on:
  workflow_dispatch:
    inputs:
      libs:
        description: "Choose specific library to test against or all"
        required: true
        type: choice
        options:
          - all
          - openai
          - langchain
          - llama_index
          - anthropic
          - aisuite
          - haystack
          - guardrails
          - dspy
          - crewai
          - genai
          - adk
          - metrics
  schedule:
    - cron: "0 0 */1 * *"
  pull_request:
    paths:
    - 'sdks/python/**'
  push:
    branches: 
      - 'main'
    paths:
      - 'sdks/python/**'

env:
  SLACK_WEBHOOK_URL: ${{ secrets.ACTION_MONITORING_SLACK }}
  LIBS: ${{ github.event.inputs.libs != '' && github.event.inputs.libs  || 'all' }}
  OPIK_SENTRY_ENABLE: False

jobs:
  init_environment:
    name: Build
    runs-on: ubuntu-latest
    outputs:
      LIBS: ${{ steps.init.outputs.LIBS }}

    steps:
      - name: Make LIBS variable global (workaround for cron)
        id: init
        run: |
          echo "LIBS=${{ env.LIBS }}" >> $GITHUB_OUTPUT

  openai_tests:
    needs: [init_environment]
    if: contains(fromJSON('["openai", "all"]'), needs.init_environment.outputs.LIBS)
    uses: ./.github/workflows/lib-openai-tests.yml
    secrets: inherit
  
  langchain_tests:
    needs: [init_environment]
    if: contains(fromJSON('["langchain", "all"]'), needs.init_environment.outputs.LIBS)
    uses: ./.github/workflows/lib-langchain-tests.yml
    secrets: inherit
  
  llama_index_tests:
    needs: [init_environment]
    if: contains(fromJSON('["llama_index", "all"]'), needs.init_environment.outputs.LIBS)
    uses: ./.github/workflows/lib-llama-index-tests.yml
    secrets: inherit

  anthropic_tests:
    needs: [init_environment]
    if: contains(fromJSON('["anthropic", "all"]'), needs.init_environment.outputs.LIBS)
    uses: ./.github/workflows/lib-anthropic-tests.yml
    secrets: inherit

  aisuite_tests:
    needs: [init_environment]
    if: contains(fromJSON('["aisuite", "all"]'), needs.init_environment.outputs.LIBS)
    uses: ./.github/workflows/lib-aisuite-tests.yml
    secrets: inherit

  haystack_tests:
    needs: [init_environment]
    if: contains(fromJSON('["haystack", "all"]'), needs.init_environment.outputs.LIBS)
    uses: ./.github/workflows/lib-haystack-tests.yml
    secrets: inherit

  guardrails_tests:
    needs: [init_environment]
    if: contains(fromJSON('["guardrails", "all"]'), needs.init_environment.outputs.LIBS)
    uses: ./.github/workflows/lib-guardrails-tests.yml
    secrets: inherit

  dspy_tests:
    needs: [init_environment]
    if: contains(fromJSON('["dspy", "all"]'), needs.init_environment.outputs.LIBS)
    uses: ./.github/workflows/lib-dspy-tests.yml
    secrets: inherit

  crewai_tests:
    needs: [init_environment]
    if: contains(fromJSON('["crewai", "all"]'), needs.init_environment.outputs.LIBS)
    uses: ./.github/workflows/lib-crewai-tests.yml
    secrets: inherit
  
  genai_tests:
    needs: [init_environment]
    if: contains(fromJSON('["genai", "all"]'), needs.init_environment.outputs.LIBS)
    uses: ./.github/workflows/lib-genai-tests.yml
    secrets: inherit

  adk_tests:
    needs: [init_environment]
    if: contains(fromJSON('["adk", "all"]'), needs.init_environment.outputs.LIBS)
    uses: ./.github/workflows/lib-adk-tests.yml
    secrets: inherit

  evaluation_metrics_tests:
    needs: [init_environment]
    if: contains(fromJSON('["metrics", "all"]'), needs.init_environment.outputs.LIBS)
    uses: ./.github/workflows/lib-metrics-with-llm-judge-tests.yml
    secrets: inherit
