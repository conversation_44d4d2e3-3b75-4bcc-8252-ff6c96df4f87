name: Model Tests
env:
  OPIK_SENTRY_ENABLE: False
  ALLURE_TOKEN: ${{ secrets.ALLURE_TOKEN }}
  ALLURE_JOB_RUN_ID: ${{ github.event.inputs.ALLURE_JOB_RUN_ID }}
  ALLURE_ENDPOINT: https://comet.testops.cloud/
  ALLURE_PROJECT_ID: 1
  ALLURE_RESULTS: allure-results

on:
  workflow_dispatch:
    inputs:
      ALLURE_JOB_RUN_ID:
        description: ALLURE_JOB_RUN_ID service parameter. Leave blank.
      ALLURE_USERNAME:
        description: ALLURE_USERNAME service parameter. Leave blank.

  pull_request:
    paths:
      - 'tests_end_to_end/models_config.yaml'
      - 'tests_end_to_end/tests/Playground/**'
      - 'tests_end_to_end/tests/OnlineScoring/**'
      - 'tests_end_to_end/tests/model_config_loader.py'

run-name: LLM Model Tests

jobs:
  model_tests:
    name: "Run LLM model tests"
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
      
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.12

      - name: Install Opik
        run: pip install ${{ github.workspace }}/sdks/python

      - name: Install Test Dependencies
        run: |
          pip install -r ${{ github.workspace }}/tests_end_to_end/test_requirements.txt
          pip install allure-pytest
          playwright install
    
      - name: Install allurectl
        uses: allure-framework/setup-allurectl@v1

      - name: Install Opik (Local)
        env:
          OPIK_USAGE_REPORT_ENABLED: false
        run: |
          cd ${{ github.workspace }}/deployment/docker-compose
          docker compose up -d --build
          
      - name: Check Docker pods are up (Local)
        run: |
          chmod +x ./tests_end_to_end/installer_utils/check_docker_compose_pods.sh
          ./tests_end_to_end/installer_utils/check_docker_compose_pods.sh
        shell: bash

      - name: Check backend health (Local)
        run: |
          chmod +x ./tests_end_to_end/installer_utils/check_backend.sh
          ./tests_end_to_end/installer_utils/check_backend.sh
        shell: bash

      - name: Check app is up via the UI (Local)
        run: |
          pytest -v -s ${{ github.workspace }}/tests_end_to_end/installer_utils/test_app_status.py
      
      - name: Run model tests
        env:
          OPIK_BASE_URL: http://localhost:5173
          OPIK_TEST_WORKSPACE: default
          OPIK_TEST_PROJECT_NAME: automated_tests_project
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        run: |
          cd ${{ github.workspace }}/tests_end_to_end
          export PYTHONPATH='.'

          allurectl watch -- pytest -s -v -m "llm_models" --browser chromium --alluredir="${ALLURE_RESULTS}"
          
      - name: Stop Opik server (Local)
        if: always()
        run: |
          cd ${{ github.workspace }}/deployment/docker-compose
          docker compose down
          cd -

    continue-on-error: ${{ github.event_name == 'pull_request' }}