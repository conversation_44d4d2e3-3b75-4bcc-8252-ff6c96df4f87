---
title: "Opik Agent Optimizer API Reference"
subtitle: "Technical SDK reference guide"
pytest_codeblocks_skip: true
---

The Opik Agent Optimizer SDK provides a set of tools for optimizing LLM prompts. This reference
guide will help you understand the available APIs and how to use them effectively.

## FewShotBayesianOptimizer

```python
FewShotBayesianOptimizer(
    model: str,
    min_examples: int = 2,
    max_examples: int = 8,
    seed: int = 42,
    n_threads: int = 8,
    verbose: int = 1,
    model_kwargs: Any
)
```

**Parameters:**

<ParamField path="model" type="str">
  The model to used to evaluate the prompt
</ParamField>
<ParamField path="min_examples" type="int" optional={true} defaultValue="2">
  Minimum number of examples to include
</ParamField>
<ParamField path="max_examples" type="int" optional={true} defaultValue="8">
  Maximum number of examples to include
</ParamField>
<ParamField path="seed" type="int" optional={true} defaultValue="42">
  Random seed for reproducibility
</ParamField>
<ParamField path="n_threads" type="int" optional={true} defaultValue="8">
  Number of threads for parallel evaluation
</ParamField>
<ParamField path="verbose" type="int" optional={true} defaultValue="1">
  Controls internal logging/progress bars (0=off, 1=on).
</ParamField>
<ParamField path="model_kwargs" type="Any" />

### Methods

#### evaluate_prompt

```python
evaluate_prompt(
    prompt: ChatPrompt,
    dataset: Dataset,
    metric: Callable,
    n_threads: int,
    verbose: int = 1,
    dataset_item_ids: Optional = None,
    experiment_config: Optional = None,
    n_samples: Optional = None,
    seed: Optional = None,
    agent_class: Optional = None
)
```

**Parameters:**

<ParamField path="prompt" type="ChatPrompt" />
<ParamField path="dataset" type="Dataset" />
<ParamField path="metric" type="Callable" />
<ParamField path="n_threads" type="int" />
<ParamField path="verbose" type="int" optional={true} defaultValue="1" />
<ParamField path="dataset_item_ids" type="Optional" optional={true} />
<ParamField path="experiment_config" type="Optional" optional={true} />
<ParamField path="n_samples" type="Optional" optional={true} />
<ParamField path="seed" type="Optional" optional={true} />
<ParamField path="agent_class" type="Optional" optional={true} />

#### get_history

```python
get_history()
```

#### optimize_prompt

```python
optimize_prompt(
    prompt: ChatPrompt,
    dataset: Dataset,
    metric: Callable,
    n_trials: int = 10,
    agent_class: Optional = None,
    experiment_config: Optional = None,
    n_samples: Optional = None
)
```

**Parameters:**

<ParamField path="prompt" type="ChatPrompt" />
<ParamField path="dataset" type="Dataset">
  Opik Dataset to optimize on
</ParamField>
<ParamField path="metric" type="Callable">
  Metric function to evaluate on
</ParamField>
<ParamField path="n_trials" type="int" optional={true} defaultValue="10">
  Number of trials for Bayesian Optimization
</ParamField>
<ParamField path="agent_class" type="Optional" optional={true} />
<ParamField path="experiment_config" type="Optional" optional={true}>
  Optional configuration for the experiment, useful to log additional metadata
</ParamField>
<ParamField path="n_samples" type="Optional" optional={true}>
  Optional number of items to test in the dataset
</ParamField>

#### update_optimization

```python
update_optimization(
    optimization: Optimization,
    status: str
)
```

**Parameters:**

<ParamField path="optimization" type="Optimization" />
<ParamField path="status" type="str" />

## MetaPromptOptimizer

```python
MetaPromptOptimizer(
    model: str,
    reasoning_model: Optional = None,
    rounds: int = 3,
    num_prompts_per_round: int = 4,
    num_threads: Optional = None,
    verbose: int = 1,
    enable_context: bool = True,
    n_threads: int = 12,
    model_kwargs: Any
)
```

**Parameters:**

<ParamField path="model" type="str">
  The model to use for evaluation
</ParamField>
<ParamField path="reasoning_model" type="Optional" optional={true}>
  The model to use for reasoning and prompt generation
</ParamField>
<ParamField path="rounds" type="int" optional={true} defaultValue="3">
  Number of optimization rounds
</ParamField>
<ParamField path="num_prompts_per_round" type="int" optional={true} defaultValue="4">
  Number of prompts to generate per round
</ParamField>
<ParamField path="num_threads" type="Optional" optional={true} />
<ParamField path="verbose" type="int" optional={true} defaultValue="1">
  Controls internal logging/progress bars (0=off, 1=on).
</ParamField>
<ParamField path="enable_context" type="bool" optional={true} defaultValue="True">
  Whether to include task-specific context (metrics, examples) in the reasoning prompt.
</ParamField>
<ParamField path="n_threads" type="int" optional={true} defaultValue="12">
  Number of threads for parallel evaluation
</ParamField>
<ParamField path="model_kwargs" type="Any" />

### Methods

#### evaluate_prompt

```python
evaluate_prompt(
    prompt: ChatPrompt,
    dataset: Dataset,
    metric: Callable,
    n_threads: int,
    verbose: int = 1,
    dataset_item_ids: Optional = None,
    experiment_config: Optional = None,
    n_samples: Optional = None,
    seed: Optional = None,
    agent_class: Optional = None
)
```

**Parameters:**

<ParamField path="prompt" type="ChatPrompt" />
<ParamField path="dataset" type="Dataset" />
<ParamField path="metric" type="Callable" />
<ParamField path="n_threads" type="int" />
<ParamField path="verbose" type="int" optional={true} defaultValue="1" />
<ParamField path="dataset_item_ids" type="Optional" optional={true} />
<ParamField path="experiment_config" type="Optional" optional={true} />
<ParamField path="n_samples" type="Optional" optional={true} />
<ParamField path="seed" type="Optional" optional={true} />
<ParamField path="agent_class" type="Optional" optional={true} />

#### get_history

```python
get_history()
```

#### optimize_prompt

```python
optimize_prompt(
    prompt: ChatPrompt,
    dataset: Dataset,
    metric: Callable,
    experiment_config: Optional = None,
    n_samples: Optional = None,
    auto_continue: bool = False,
    agent_class: Optional = None,
    kwargs: Any
)
```

**Parameters:**

<ParamField path="prompt" type="ChatPrompt" />
<ParamField path="dataset" type="Dataset">
  The dataset to evaluate against
</ParamField>
<ParamField path="metric" type="Callable">
  The metric to use for evaluation
</ParamField>
<ParamField path="experiment_config" type="Optional" optional={true}>
  A dictionary to log with the experiments
</ParamField>
<ParamField path="n_samples" type="Optional" optional={true}>
  The number of dataset items to use for evaluation
</ParamField>
<ParamField path="auto_continue" type="bool" optional={true} defaultValue="False">
  If True, the algorithm may continue if goal not met
</ParamField>
<ParamField path="agent_class" type="Optional" optional={true} />
<ParamField path="kwargs" type="Any" />

#### update_optimization

```python
update_optimization(
    optimization: Optimization,
    status: str
)
```

**Parameters:**

<ParamField path="optimization" type="Optimization" />
<ParamField path="status" type="str" />

## EvolutionaryOptimizer

```python
EvolutionaryOptimizer(
    model: str,
    population_size: int = 30,
    num_generations: int = 15,
    mutation_rate: float = 0.2,
    crossover_rate: float = 0.8,
    tournament_size: int = 4,
    num_threads: Optional = None,
    elitism_size: int = 3,
    adaptive_mutation: bool = True,
    enable_moo: bool = True,
    enable_llm_crossover: bool = True,
    seed: Optional = 42,
    output_style_guidance: Optional = None,
    infer_output_style: bool = False,
    verbose: int = 1,
    n_threads: int = 12,
    model_kwargs: Any
)
```

**Parameters:**

<ParamField path="model" type="str">
  The model to use for evaluation
</ParamField>
<ParamField path="population_size" type="int" optional={true} defaultValue="30">
  Number of prompts in the population
</ParamField>
<ParamField path="num_generations" type="int" optional={true} defaultValue="15">
  Number of generations to run
</ParamField>
<ParamField path="mutation_rate" type="float" optional={true} defaultValue="0.2">
  Mutation rate for genetic operations
</ParamField>
<ParamField path="crossover_rate" type="float" optional={true} defaultValue="0.8">
  Crossover rate for genetic operations
</ParamField>
<ParamField path="tournament_size" type="int" optional={true} defaultValue="4">
  Tournament size for selection
</ParamField>
<ParamField path="num_threads" type="Optional" optional={true} />
<ParamField path="elitism_size" type="int" optional={true} defaultValue="3">
  Number of elitism prompts
</ParamField>
<ParamField path="adaptive_mutation" type="bool" optional={true} defaultValue="True">
  Whether to use adaptive mutation
</ParamField>
<ParamField path="enable_moo" type="bool" optional={true} defaultValue="True">
  Whether to enable multi-objective optimization - When enable optimizes for both the supplied metric and the length of
  the prompt
</ParamField>
<ParamField path="enable_llm_crossover" type="bool" optional={true} defaultValue="True">
  Whether to enable LLM crossover
</ParamField>
<ParamField path="seed" type="Optional" optional={true} defaultValue="42">
  Random seed for reproducibility
</ParamField>
<ParamField path="output_style_guidance" type="Optional" optional={true}>
  Output style guidance for prompts
</ParamField>
<ParamField path="infer_output_style" type="bool" optional={true} defaultValue="False">
  Whether to infer output style
</ParamField>
<ParamField path="verbose" type="int" optional={true} defaultValue="1">
  Controls internal logging/progress bars (0=off, 1=on).
</ParamField>
<ParamField path="n_threads" type="int" optional={true} defaultValue="12">
  Number of threads for parallel evaluation
</ParamField>
<ParamField path="model_kwargs" type="Any" />

### Methods

#### evaluate_prompt

```python
evaluate_prompt(
    prompt: ChatPrompt,
    dataset: Dataset,
    metric: Callable,
    n_threads: int,
    verbose: int = 1,
    dataset_item_ids: Optional = None,
    experiment_config: Optional = None,
    n_samples: Optional = None,
    seed: Optional = None,
    agent_class: Optional = None
)
```

**Parameters:**

<ParamField path="prompt" type="ChatPrompt" />
<ParamField path="dataset" type="Dataset" />
<ParamField path="metric" type="Callable" />
<ParamField path="n_threads" type="int" />
<ParamField path="verbose" type="int" optional={true} defaultValue="1" />
<ParamField path="dataset_item_ids" type="Optional" optional={true} />
<ParamField path="experiment_config" type="Optional" optional={true} />
<ParamField path="n_samples" type="Optional" optional={true} />
<ParamField path="seed" type="Optional" optional={true} />
<ParamField path="agent_class" type="Optional" optional={true} />

#### get_history

```python
get_history()
```

#### get_llm_crossover_system_prompt

```python
get_llm_crossover_system_prompt()
```

#### optimize_prompt

```python
optimize_prompt(
    prompt: ChatPrompt,
    dataset: Dataset,
    metric: Callable,
    experiment_config: Optional = None,
    n_samples: Optional = None,
    auto_continue: bool = False,
    agent_class: Optional = None,
    kwargs: Any
)
```

**Parameters:**

<ParamField path="prompt" type="ChatPrompt">
  The prompt to optimize
</ParamField>
<ParamField path="dataset" type="Dataset">
  The dataset to use for evaluation
</ParamField>
<ParamField path="metric" type="Callable">
  Metric function to optimize with, should have the arguments `dataset_item` and `llm_output`
</ParamField>
<ParamField path="experiment_config" type="Optional" optional={true}>
  Optional experiment configuration
</ParamField>
<ParamField path="n_samples" type="Optional" optional={true}>
  Optional number of samples to use
</ParamField>
<ParamField path="auto_continue" type="bool" optional={true} defaultValue="False">
  Whether to automatically continue optimization
</ParamField>
<ParamField path="agent_class" type="Optional" optional={true} />
<ParamField path="kwargs" type="Any" />

#### update_optimization

```python
update_optimization(
    optimization: Optimization,
    status: str
)
```

**Parameters:**

<ParamField path="optimization" type="Optimization" />
<ParamField path="status" type="str" />

## ChatPrompt

```python
ChatPrompt(
    name: str = 'chat-prompt',
    system: Optional = None,
    user: Optional = None,
    messages: Optional = None,
    tools: Optional = None,
    function_map: Optional = None,
    model: Optional = None,
    invoke: Optional = None,
    project_name: Optional = 'Default Project',
    model_kwargs: Any
)
```

**Parameters:**

<ParamField path="name" type="str" optional={true} defaultValue="chat-prompt" />
<ParamField path="system" type="Optional" optional={true} />
<ParamField path="user" type="Optional" optional={true} />
<ParamField path="messages" type="Optional" optional={true} />
<ParamField path="tools" type="Optional" optional={true} />
<ParamField path="function_map" type="Optional" optional={true} />
<ParamField path="model" type="Optional" optional={true} />
<ParamField path="invoke" type="Optional" optional={true} />
<ParamField path="project_name" type="Optional" optional={true} defaultValue="Default Project" />
<ParamField path="model_kwargs" type="Any" />

### Methods

#### copy

```python
copy()
```

#### get_messages

```python
get_messages(
    dataset_item: Optional = None
)
```

**Parameters:**

<ParamField path="dataset_item" type="Optional" optional={true} />

#### set_messages

```python
set_messages(
    messages: List
)
```

**Parameters:**

<ParamField path="messages" type="List" />

#### to_dict

```python
to_dict()
```

## OptimizationResult

```python
OptimizationResult(
    optimizer: <class 'str'> = 'Optimizer',
    prompt: List[Dict[str, str]],
    score: <class 'float'>,
    metric_name: <class 'str'>,
    optimization_id: Optional[str] = None,
    dataset_id: Optional[str] = None,
    initial_prompt: Optional[List[Dict[str, str]]] = None,
    initial_score: Optional[float] = None,
    details: Dict[str, Any] = PydanticUndefined,
    history: List[Dict[str, Any]] = [],
    llm_calls: Optional[int] = None,
    demonstrations: Optional[List[Dict[str, Any]]] = None,
    mipro_prompt: Optional[str] = None,
    tool_prompts: Optional[Dict[str, str]] = None
)
```

**Parameters:**

<ParamField path="optimizer" type="<class 'str'>" optional={true} defaultValue="Optimizer" />
<ParamField path="prompt" type="List[Dict[str, str]]" defaultValue="PydanticUndefined" />
<ParamField path="score" type="<class 'float'>" defaultValue="PydanticUndefined" />
<ParamField path="metric_name" type="<class 'str'>" defaultValue="PydanticUndefined" />
<ParamField path="optimization_id" type="Optional[str]" optional={true} />
<ParamField path="dataset_id" type="Optional[str]" optional={true} />
<ParamField path="initial_prompt" type="Optional[List[Dict[str, str]]]" optional={true} />
<ParamField path="initial_score" type="Optional[float]" optional={true} />
<ParamField path="details" type="Dict[str, Any]" optional={true} defaultValue="PydanticUndefined" />
<ParamField path="history" type="List[Dict[str, Any]]" optional={true} defaultValue="[]" />
<ParamField path="llm_calls" type="Optional[int]" optional={true} />
<ParamField path="demonstrations" type="Optional[List[Dict[str, Any]]]" optional={true} />
<ParamField path="mipro_prompt" type="Optional[str]" optional={true} />
<ParamField path="tool_prompts" type="Optional[Dict[str, str]]" optional={true} />
