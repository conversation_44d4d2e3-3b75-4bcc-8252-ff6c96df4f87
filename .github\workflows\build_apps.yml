name: "Build Opik Docker Images"
run-name: "Build ${{ github.ref_name }} by @${{ github.actor }}"

on:
  push:
    branches: 
      - 'main'
    paths-ignore: 
      - "deployment/**"
      - 'sdks/**'

  workflow_dispatch:
    inputs:
      version:
        type: string
        required: false
        description: Version (optional)
        default: ""
      is_release:
        type: boolean
        required: true
        description: Is release build
        default: false
  workflow_call:
    inputs:
      version:
        type: string
        required: true
        description: Version
        default: ""
      is_release:
        type: boolean
        required: true
        description: Is release build
        default: false

jobs:

  set-version:
    runs-on: ubuntu-latest
    outputs:
        version: ${{ steps.version.outputs.version }}
        build_from: ${{ steps.version.outputs.build_from }}
    steps:
        - name: Checkout
          uses: actions/checkout@v4.1.1

        - name: Set version
          id: version
          run: |
            if [[ "${{inputs.version}}" != "" ]]; then
              VERSION=${{inputs.version}}
            else
              BASE_VERSION=$(cat version.txt)
              BRANCH_NAME="${{ github.ref_name }}"
              if [[ "${BRANCH_NAME}" == "main" ]]; then
                VERSION=${BASE_VERSION}-${{ github.run_number }}
              elif [[ "${BRANCH_NAME}" =~ ^hotfix/.* ]]; then
                # Special handling for hotfix branches
                VERSION="${BASE_VERSION}-hotfix.${{ github.run_number }}"
              else
                # Normalize branch name
                fixedBranchName=$(echo "${BRANCH_NAME}" | sed 's/origin\///; s/\//-/g; s/_/-/g; s/.*/\L&/')
                if [ ${#fixedBranchName} -gt 21 ]; then
                  fixedBranchName="${fixedBranchName:0:20}"
                  # remove dash at the end
                  while [ "${fixedBranchName: -1}" = "-" ]; do
                    fixedBranchName="${fixedBranchName%?}"
                  done
                fi
                VERSION="${BASE_VERSION}.${fixedBranchName}-${{ github.run_number }}"
              fi
            fi
            echo "version=${VERSION}" | tee -a $GITHUB_OUTPUT
            echo "Version is ${VERSION}" >> $GITHUB_STEP_SUMMARY

            if [[ "${{inputs.version}}" == ""  ]]; then
              echo "build_from=${{github.ref_name}}" | tee -a $GITHUB_OUTPUT
            else
              echo "build_from=${{inputs.version}}" | tee -a $GITHUB_OUTPUT
            fi
            
  build-backend:
    needs:
      - set-version
    uses: ./.github/workflows/build_and_push_docker.yaml
    with:
      image: "opik-backend"
      version: ${{needs.set-version.outputs.version}}
      build_from: ${{needs.set-version.outputs.build_from}}
      build_comet_image: false
      comet_build_args: ""
      is_release: ${{ inputs.is_release || false }}

  build-sandbox-executor-python:
    needs:
      - set-version
    uses: ./.github/workflows/build_and_push_docker.yaml
    with:
      image: "opik-sandbox-executor-python"
      version: ${{needs.set-version.outputs.version}}
      build_from: ${{needs.set-version.outputs.build_from}}
      build_comet_image: false
      comet_build_args: ""
      is_release: ${{ inputs.is_release || false }}

  build-python-backend:
    needs:
      - set-version
      - build-sandbox-executor-python
    uses: ./.github/workflows/build_and_push_docker.yaml
    with:
      image: "opik-python-backend"
      version: ${{needs.set-version.outputs.version}}
      build_from: ${{needs.set-version.outputs.build_from}}
      build_comet_image: false
      comet_build_args: ""
      is_release: ${{ inputs.is_release || false }}

  build-guardrails-backend:
    needs:
      - set-version
    uses: ./.github/workflows/build_and_push_docker.yaml
    with:
      image: "opik-guardrails-backend"
      version: ${{needs.set-version.outputs.version}}
      build_from: ${{needs.set-version.outputs.build_from}}
      build_comet_image: false
      comet_build_args: ""
      is_release: ${{ inputs.is_release || false }}

  build-frontend:
    needs:
      - set-version
    secrets: inherit
    uses: ./.github/workflows/build_and_push_docker.yaml
    with:
      image: "opik-frontend"
      version: ${{needs.set-version.outputs.version}}
      build_from: ${{needs.set-version.outputs.build_from}}
      build_comet_image: true
      comet_build_args: "BUILD_MODE=comet"
      is_release: ${{ inputs.is_release || false }}
      
  create-git-tag:
    if: ${{ !(inputs.is_release || false) }}
    needs:
      - set-version
      - build-backend
      - build-frontend
      - build-python-backend
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Create git tag
        run: |
          set -x
          git config --local user.email "<EMAIL>"
          git config --local user.name "github-actions"
          git tag ${{needs.set-version.outputs.version}}
          git push --no-verify origin refs/tags/${{needs.set-version.outputs.version}}
        
    
