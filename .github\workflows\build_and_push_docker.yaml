name: "Build Docker Images"

on:
  workflow_call:
    inputs:
        image:
            type: string
            required: true
            description: Docker Image
        version:
            type: string
            required: true
            description: Version
        build_from:
            type: string
            required: true
            description: Original version to build from 
            default: ""
        build_comet_image:
            type: boolean
            required: true
            description: If to build a Comet integration image
            default: false
        comet_build_args:
            type: string
            required: false
            default: ""
            description: Arguments for cloud docker build
        is_release:
            type: boolean
            required: true
            description: If this is a release build (will build multi-arch and push latest tag)
            default: false
    secrets:
      OPIK_FE_SENTRY_DSN:
        required: false

env:
  DOCKER_REGISTRY: "ghcr.io/comet-ml/opik" 

jobs:

  build-n-push-image:
    runs-on: ubuntu-latest
    steps:
        - name: Checkout
          uses: actions/checkout@v4.1.1
          with:
            ref: ${{inputs.build_from}}

        - name: Save opik-sandbox-executor-python
          if: inputs.image == 'opik-python-backend'
          run: |
            docker pull ${{env.DOCKER_REGISTRY}}/opik-sandbox-executor-python:${{inputs.version}}
            docker save ${{env.DOCKER_REGISTRY}}/opik-sandbox-executor-python:${{inputs.version}} | gzip > apps/opik-python-backend/opik-sandbox-executor-python.tar.gz
            
        - name: Login to GHCR
          uses: docker/login-action@v3
          with:
            registry: ${{env.DOCKER_REGISTRY}}
            username: "github-actions"
            password: ${{ secrets.GITHUB_TOKEN }}

        - name: Set up Docker Buildx
          uses: docker/setup-buildx-action@v2

        - name: Build and Push Docker Image
          uses: docker/build-push-action@v4
          with:
            context: apps/${{ inputs.image }}/
            platforms: ${{ (inputs.is_release && inputs.image != 'opik-guardrails-backend') && 'linux/amd64,linux/arm64' || 'linux/amd64' }}
            push: true
            tags: |
              ${{env.DOCKER_REGISTRY}}/${{ inputs.image }}:${{inputs.version}}
              ${{ inputs.is_release && format('{0}/{1}:latest', env.DOCKER_REGISTRY, inputs.image) || '' }}
            build-args: |
              OPIK_VERSION=${{inputs.version}}

        - name: Build and Push Docker Image for Comet integration
          if: inputs.build_comet_image
          uses: docker/build-push-action@v4
          with:
            context: apps/${{ inputs.image }}/
            platforms: ${{ inputs.is_release && 'linux/amd64,linux/arm64' || 'linux/amd64' }}
            push: true
            tags: |
              ${{env.DOCKER_REGISTRY}}/${{ inputs.image }}-comet:${{inputs.version}}
              ${{ inputs.is_release && format('{0}/{1}-comet:latest', env.DOCKER_REGISTRY, inputs.image) || '' }}
            build-args: |
              ${{inputs.comet_build_args}}
              OPIK_VERSION=${{inputs.version}}
              SENTRY_ENABLED=true
              SENTRY_DSN=${{secrets.OPIK_FE_SENTRY_DSN}}
            secrets: |
              OPIK_FE_SENTRY_DSN=${{secrets.OPIK_FE_SENTRY_DSN}}
              GITHUB_TOKEN=${{ secrets.GITHUB_TOKEN }}

        - name: Write Build Summary
          run: |
            echo "### Docker images pushed:" >> $GITHUB_STEP_SUMMARY
            echo "${{env.DOCKER_REGISTRY}}/${{ inputs.image }}:${{inputs.version}}" >> $GITHUB_STEP_SUMMARY
            if [[ "${{ inputs.is_release }}" == "true" ]]; then
              echo "${{env.DOCKER_REGISTRY}}/${{ inputs.image }}:latest" >> $GITHUB_STEP_SUMMARY
              if [[ "${{ inputs.image }}" == "guardrails" ]]; then
                echo "Built for platforms: linux/amd64" >> $GITHUB_STEP_SUMMARY
              else
                echo "Built for platforms: linux/amd64, linux/arm64" >> $GITHUB_STEP_SUMMARY
              fi
            else
              echo "Built for platforms: linux/amd64" >> $GITHUB_STEP_SUMMARY
            fi
            
            if [[ "${{ inputs.build_comet_image }}" == "true" ]]; then
              echo "### Comet Integration Image" >> $GITHUB_STEP_SUMMARY
              echo "${{env.DOCKER_REGISTRY}}/${{ inputs.image }}-comet:${{inputs.version}}" >> $GITHUB_STEP_SUMMARY
              if [[ "${{ inputs.is_release }}" == "true" ]]; then
                echo "${{env.DOCKER_REGISTRY}}/${{ inputs.image }}-comet:latest" >> $GITHUB_STEP_SUMMARY
                echo "Built for platforms: linux/amd64, linux/arm64" >> $GITHUB_STEP_SUMMARY
              else
                echo "Built for platforms: linux/amd64" >> $GITHUB_STEP_SUMMARY
              fi
            fi
        