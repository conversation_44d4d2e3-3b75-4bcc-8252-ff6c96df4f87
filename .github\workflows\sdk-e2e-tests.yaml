name: SDK E2E Tests
run-name: "SDK E2E Tests ${{ github.ref_name }} by @${{ github.actor }}"
on:
    workflow_dispatch:
    pull_request:
        paths:
          - 'sdks/python/**'
          - 'apps/opik-backend/**'
    push:
        branches: 
          - 'main'
        paths: 
          - 'sdks/python/**'
          - 'apps/opik-backend/**'
env:
  OPIK_ENABLE_LITELLM_MODELS_MONITORING: False
  OPIK_SENTRY_ENABLE: False
  OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
  OPENAI_ORG_ID: ${{ secrets.OPENAI_ORG_ID }}
jobs:
    run-e2e:
        name: SDK E2E Tests ${{matrix.python_version}}
        runs-on: ubuntu-latest   
        strategy:
            fail-fast: false
            matrix:
                python_version: [
                  "3.8",
                  "3.9",
                  "3.10",
                  "3.11",
                  "3.12"
                ]

        steps:
        - name: Checkout
          uses: actions/checkout@v4.1.1
            
        - name: Setup Python ${{matrix.python_version}}
          uses: actions/setup-python@v5
          with:
            python-version: ${{matrix.python_version}}
      
        - name: Run latest Opik server
          env:
            OPIK_USAGE_REPORT_ENABLED: false
          run: |
            cd deployment/docker-compose
            export OPIK_FRONTEND_FLAVOR=guardrails
            docker compose --profile guardrails up -d --build
        
        - name: Check Opik server availability
          shell: bash
          run: |
            chmod +x ${{ github.workspace }}/tests_end_to_end/installer_utils/*.sh
            cd ${{ github.workspace }}/deployment/docker-compose
            echo "Check Docker pods are up"
            ${{ github.workspace }}/tests_end_to_end/installer_utils/check_docker_compose_pods.sh
            echo "Check backend health"
            ${{ github.workspace }}/tests_end_to_end/installer_utils/check_backend.sh

        - name: Install opik SDK
          run: |
            cd ${{ github.workspace }}/sdks/python
            pip install -r tests/test_requirements.txt
            pip install .

        - name: Run tests
          run: |
            cd ${{ github.workspace }}/sdks/python
            export OPIK_URL_OVERRIDE=http://localhost:5173/api
            export OPIK_CONSOLE_LOGGING_LEVEL=DEBUG
            pytest tests/e2e -vv --junitxml=${{ github.workspace }}/test_results_${{matrix.python_version}}.xml

        - name: Publish Test Report
          uses: EnricoMi/publish-unit-test-result-action/linux@v2
          if: failure()
          with:
            action_fail: true
            check_name: SDK E2E Tests Results
            files: ${{ github.workspace }}/test_results_${{matrix.python_version}}.xml

        - name: Keep BE log in case of failure
          if: failure()
          run: |
            docker logs opik-backend-1 > ${{ github.workspace }}/opik-backend_p${{matrix.python_version}}.log

        - name: Attach BE log
          if: failure()
          uses: actions/upload-artifact@v4
          with:
              name: opik-backend-log-p${{matrix.python_version}}
              path: ${{ github.workspace }}/opik-backend_p${{matrix.python_version}}.log

        - name: Stop opik server
          if: always()
          run: |
            cd ${{ github.workspace }}/deployment/docker-compose
            docker compose down

