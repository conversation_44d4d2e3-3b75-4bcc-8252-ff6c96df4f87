# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file

version: 2
updates:
 - package-ecosystem: "maven"
   directory: "apps/opik-backend"
   schedule:
     interval: "weekly"      # How often to check for updates
   ignore:
     - dependency-name: "*"
       update-types: ["version-update:semver-patch"] 
   open-pull-requests-limit: 5
