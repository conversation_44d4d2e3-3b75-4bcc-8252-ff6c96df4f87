**Opik Dashboard**:

**Python and JS / TS SDK**:

- Added support for streaming in ADK integration
- Add cost tracking for the ADK integration
- Add support for OpenAI `responses.parse`
- Reduce the memory and CPU overhead of the Python SDK through various
  performance optimizations

**Deployments**:

- Updated port mapping when using `opik.sh`
- Fixed persistence when using Docker compose deployments

_Release_: `1.7.15`, `1.7.16`, `1.7.17`, `1.7.18`
