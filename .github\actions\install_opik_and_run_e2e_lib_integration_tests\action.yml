---
name: Install OPIK SDK and run E2E lib tests
description: Install OPIK SDK with test dependencies and executes E2E integration tests for particular library
inputs:
  library_name:
    required: true
    description: "The library name"
  python_version:
    required: true
    description: "Python version"

runs:
  using: composite
  steps:
    - name: Install OPIK, test requirements, and run tests for ${{ inputs.library_name }}
      shell: bash
      run: |
        # create virtual environment
        uv venv ${{ inputs.library_name }}-env --python ${{ inputs.python_version }}
        source ${{ inputs.library_name }}-env/bin/activate
        
        cd ${{ github.workspace }}/sdks/python
        
        echo "Installing OPIK"
        uv pip install .   
        
        echo "Installing test dependencies"
        cd ./tests
        uv pip install --no-cache-dir --disable-pip-version-check -r test_requirements.txt
        uv pip install --no-cache-dir --disable-pip-version-check -r e2e_library_integration/${{ inputs.library_name }}/requirements.txt
        uv pip list
        
        echo "Running tests"
        export OPIK_URL_OVERRIDE=http://localhost:5173/api
        export OPIK_CONSOLE_LOGGING_LEVEL=DEBUG
        cd ./e2e_library_integration/${{ inputs.library_name }}/
        python -m pytest -vv .
        
        # Deactivate virtual environment
        deactivate