name: "Backend Formatting Check"
run-name: "Backend Formatting Check on ${{ github.ref_name }} by @${{ github.actor }}"

on:
  pull_request:
    paths:
      - "apps/opik-backend/**/*.java"
  push:
    branches:
      - "main"
    paths:
      - "apps/opik-backend/**/*.java"

  workflow_dispatch:

jobs:
  run-backend-formatting-check:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: apps/opik-backend/
    steps:
      - name: Checkout
        uses: actions/checkout@v4.1.1
        with:
          fetch-depth: 1

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: "21"
          distribution: "corretto"
          cache: maven

      - name: Run Formatting Check for backend
        run: mvn clean spotless:check
