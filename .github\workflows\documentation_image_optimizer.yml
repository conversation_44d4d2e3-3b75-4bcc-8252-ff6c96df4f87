name: Docs - Compress Images
on:
  workflow_dispatch:
  pull_request:
    # Run Image Actions when JPG, JPEG, PNG or WebP files are added or changed.
    # See https://help.github.com/en/actions/automating-your-workflow-with-github-actions/workflow-syntax-for-github-actions#onpushpull_requestpaths for reference.
    paths:
      [
        "apps/opik-documentation/documentation/fern/img/**.jpg",
        "apps/opik-documentation/documentation/fern/img/**.jpeg",
        "apps/opik-documentation/documentation/fern/img/**.png",
        "apps/opik-documentation/documentation/fern/img/**.webp",
      ]
jobs:
  build:
    # Only run on non-draft PRs within the same repository.
    if:
      github.event.pull_request.head.repo.full_name == github.repository && github.event.pull_request.draft
      == false
    name: calibreapp/image-actions
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4.2.2
          
      - name: Compress Images
        uses: calibreapp/image-actions@1.1.0
        with:
          # The `GITHUB_TOKEN` is automatically generated by GitHub and scoped only to the repository that is currently running the action. By default, the action can’t update Pull Requests initiated from forked repositories.
          # See https://docs.github.com/en/actions/reference/authentication-in-a-workflow and https://help.github.com/en/articles/virtual-environments-for-github-actions#token-permissions
          githubToken: ${{ secrets.GITHUB_TOKEN }}
          ignorePaths: "apps/opik-frontend/**,apps/opik-backend/**,apps/opik-python-backend/**"
