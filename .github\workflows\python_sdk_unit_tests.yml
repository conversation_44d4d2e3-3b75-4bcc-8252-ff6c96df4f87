name: SDK Unit Tests
run-name: "SDK Unit Tests ${{ github.ref_name }} by @${{ github.actor }}"
on:
  pull_request:
    paths:
    - 'sdks/python/**'
  push:
    branches: 
      - 'main'
    paths:
      - 'sdks/python/**'
env:
  OPIK_ENABLE_LITELLM_MODELS_MONITORING: False
  OPIK_SENTRY_ENABLE: False
jobs:
  UnitTests:
    name: Units_Python_${{matrix.python_version}}
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: sdks/python/

    strategy:
      fail-fast: false
      matrix:
        python_version: [
          "3.8",
          "3.9",
          "3.10",
          "3.11",
          "3.12"
        ]

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Print environment variables
        run: env

      - name: Print event object
        run: cat $GITHUB_EVENT_PATH

      - name: Print the PR title
        env:
          PR_TITLE: ${{ github.event.pull_request.title }}
        run: echo "$PR_TITLE"

      - name: Setup Python ${{ matrix.python_version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python_version }}

      - name: Install opik
        run: pip install .

      - name: Install test requirements
        run: |
          cd ./tests
          pip install --no-cache-dir --disable-pip-version-check -r test_requirements.txt -r ./unit/test_requirements.txt
          pip list

      - name: Running SDK Unit Tests
        run: python -m pytest --cov=src/opik -vv tests/unit/ --junitxml=${{ github.workspace }}/test_results.xml

      - name: Publish Test Report
        uses: EnricoMi/publish-unit-test-result-action/linux@v2
        if: failure()
        with:
          action_fail: true
          check_name: SDK Unit Tests Results
          files: ${{ github.workspace }}/test_results.xml
