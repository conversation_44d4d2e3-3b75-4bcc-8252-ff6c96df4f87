name: "Release wrapper for n8n integration"
run-name: "N8N Release Wrapper from ${{ github.ref_name }} by @${{ github.actor }}"

on:
  workflow_dispatch:
    inputs:
      resumeUrl:
        description: 'URL to call to resume n8n'
        required: false

jobs:
  trigger-release:
    uses: ./.github/workflows/release.yaml  # or the path to your existing Release workflow
    secrets: inherit

  notify-n8n:
    runs-on: ubuntu-latest
    needs: trigger-release
    if: ${{ always() }}
    steps:
      - name: Determine status
        id: status
        run: |
          if [ "${{ needs.trigger-release.result }}" == "success" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
          fi

      - name: Notify n8n
        if: ${{ inputs.resumeUrl != '' }}
        run: |
          version="${{ needs.trigger-release.outputs.version }}"
          echo "Version: $version"
          echo "Status: ${{ steps.status.outputs.status }}"
          curl -X POST "${{ inputs.resumeUrl }}" \
            -H "Content-Type: application/json" \
            -d "{\"version\": \"$version\", \"status\": \"${{ steps.status.outputs.status }}\", \"run_id\": \"${{ github.run_id }}\", \"url\": \"https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}\"}"
