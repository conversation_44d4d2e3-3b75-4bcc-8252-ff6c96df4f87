name: TypeScript SDK Publish
run-name: "TypeScript SDK Publish from ${{github.ref_name}} by @${{ github.actor }}"

on:
  workflow_dispatch:
    inputs:
      version:
        type: string
        required: true
        description: Version
        default: ""
      is_release:
        type: boolean
        required: false
        default: false
  workflow_call:
    inputs:
      version:
        type: string
        required: true
        description: Version
        default: ""
      is_release:
        type: boolean
        required: false
        default: false

jobs:
  publish:
    runs-on: ubuntu-latest
    env:
      VERSION: ${{ github.event.inputs.version || inputs.version }}
      IS_RELEASE: ${{ github.event.inputs.is_release || inputs.is_release }}
    defaults:
      run:
        working-directory: sdks/typescript

    steps:
      - uses: actions/checkout@v4
        with:
          ref: main
          fetch-depth: 0
          token: ${{ secrets.GH_PAT_TO_ACCESS_GITHUB_API }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          registry-url: "https://registry.npmjs.org"

      - name: Install dependencies
        run: npm ci

      - name: Update version
        run: npm version ${{ env.VERSION }} --no-git-tag-version

      - name: Build package
        run: npm run build

      - name: Commit version changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "github-actions"
          git add package.json package-lock.json
          git commit -m "Update TypeScript SDK version to $(node -p "require('./package.json').version")"

      - name: Publish to NPM
        if: ${{ env.IS_RELEASE }}
        run: npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Push changes
        uses: ad-m/github-push-action@master
        with:
          github_token: ${{ secrets.GH_PAT_TO_ACCESS_GITHUB_API }}
          branch: ${{ github.ref }}
          force_with_lease: true
