name: Docs - Test cookbooks
on:
  workflow_dispatch:
    inputs:
      install_opik:
        description: 'Enable opik installation from source files'
        required: false
        default: 'false'
        type: choice
        options:
          - 'false'
          - 'true'
  schedule:
    - cron: '0 0 * * *'  # Run once a day at midnight UTC
jobs:
  test-notebooks:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ ubuntu-latest]
        python-version: ["3.12"]
        notebooks:
          - apps/opik-documentation/documentation/docs/cookbook/quickstart_notebook.ipynb
          - apps/opik-documentation/documentation/docs/cookbook/evaluate_hallucination_metric.ipynb
          - apps/opik-documentation/documentation/docs/cookbook/evaluate_moderation_metric.ipynb
          - apps/opik-documentation/documentation/docs/cookbook/langchain.ipynb
          - apps/opik-documentation/documentation/docs/cookbook/langgraph.ipynb
          - apps/opik-documentation/documentation/docs/cookbook/llama-index.ipynb
          - apps/opik-documentation/documentation/docs/cookbook/openai.ipynb
          - apps/opik-documentation/documentation/docs/cookbook/litellm.ipynb
          - apps/opik-documentation/documentation/docs/cookbook/ragas.ipynb
          - apps/opik-documentation/documentation/docs/cookbook/dspy.ipynb
          - apps/opik-documentation/documentation/docs/cookbook/crewai.ipynb
          - apps/opik-documentation/documentation/docs/cookbook/instructor.ipynb
    env:
      NOTEBOOK_TO_TEST: ${{ matrix.notebooks }}
      OPIK_SENTRY_ENABLE: False
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Get pip cache dir
        id: pip-cache
        run: |
          echo "::set-output name=dir::$(pip cache dir)"
      - name: pip cache
        uses: actions/cache@v3
        with:
          path: ${{ steps.pip-cache.outputs.dir }}
          key: ${{ matrix.os }}-${{ matrix.python-version }}-pip
          restore-keys: |
            ${{ matrix.os }}-${{ matrix.python-version }}-pip
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          python -m pip install -U ipython nbconvert
      - name: Install opik from source files (optional)
        if: ${{ github.event_name == 'workflow_dispatch' && inputs.install_opik == 'true' }}
        run: pip install sdks/python
      - name: Prepare env variables
        run: |
          directory=$(dirname -- "${NOTEBOOK_TO_TEST}")
          notebook=$(basename -- "${NOTEBOOK_TO_TEST}")
          echo "TEST_DIRECTORY=${directory}"  >> "$GITHUB_ENV"
          echo "TEST_NOTEBOOK=${notebook}" >> "$GITHUB_ENV"
      - name: Test notebook
        run: |
          cd "$TEST_DIRECTORY" || exit
          python -X faulthandler $(which ipython) "$TEST_NOTEBOOK"
        env:
          OPENAI_API_KEY: ${{ secrets.DOCS_OPENAI_API_KEY }}
          OPIK_API_KEY: ${{ secrets.COMET_API_KEY }}
          OPIK_WORKSPACE: ${{ secrets.COMET_WORKSPACE }}
