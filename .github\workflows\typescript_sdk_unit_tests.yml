name: TypeScript SDK Unit Tests
run-name: "TypeScript SDK Unit Tests ${{ github.ref_name }} by @${{ github.actor }}"
on:
  pull_request:
    paths:
      - "sdks/typescript/**"
  push:
    branches:
      - "main"
    paths:
      - "sdks/typescript/**"
  workflow_dispatch:

jobs:
  test:
    name: Test on Node ${{ matrix.node-version }}
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: sdks/typescript
    strategy:
      fail-fast: false
      matrix:
        node-version: [18, 20, 22]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4.2.2

      - name: Set up Node.js
        uses: actions/setup-node@v4.2.0
        with:
          node-version: ${{ matrix.node-version || '20' }}

      - name: Install dependencies (npm)
        run: npm ci

      - name: Run tests (npm)
        run: npm run test
