# The workflow to run tests for third-party libraries integrations that require OPIK server running
#
name: SDK E2E Libraries Integration Tests
run-name: "SDK E2E Libraries Integration Tests ${{ github.ref_name }} by @${{ github.actor }}"
on:
    workflow_dispatch:
      inputs:
        libs:
          description: "Choose specific library to test against or all"
          required: true
          type: choice
          options:
            - all
            - adk
            - litellm
    schedule:
      - cron: "0 0 */2 * *"
    pull_request:
        paths:
          - 'sdks/python/**'
    push:
        branches:
          - 'main'
        paths:
          - 'sdks/python/**'
env:
  OPIK_ENABLE_LITELLM_MODELS_MONITORING: True  # unlike other workflows, here we need it for tests
  OPIK_SENTRY_ENABLE: False
  SLACK_WEBHOOK_URL: ${{ secrets.ACTION_MONITORING_SLACK }}
  LIBS: ${{ github.event.inputs.libs != '' && github.event.inputs.libs  || 'all' }}

jobs:
  run_e2e_lib_integration:
    name: E2E Lib Integration Python ${{matrix.python_version}}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python_version: [
          "3.9",
          "3.10",
          "3.11",
          "3.12"
        ]

    steps:
      - name: Checkout
        uses: actions/checkout@v4.1.1
          
      - name: Install uv and set the Python version ${{ matrix.python_version }}
        uses: astral-sh/setup-uv@v5
        with:
          python-version: ${{ matrix.python_version }}

      - name: Make LIBS variable global (workaround for cron)
        id: init
        run: |
          echo "LIBS=${{ env.LIBS }}" >> $GITHUB_OUTPUT

      - name: Run latest Opik server
        env:
          OPIK_USAGE_REPORT_ENABLED: false
        shell: bash
        run: |
          cd ${{ github.workspace }}/deployment/docker-compose
          docker compose up -d --build

      - name: Check Opik server availability
        shell: bash
        run: |
          chmod +x ${{ github.workspace }}/tests_end_to_end/installer_utils/*.sh
          cd ${{ github.workspace }}/deployment/docker-compose
          echo "Check Docker pods are up"
          ${{ github.workspace }}/tests_end_to_end/installer_utils/check_docker_compose_pods.sh
          echo "Check backend health"
          ${{ github.workspace }}/tests_end_to_end/installer_utils/check_backend.sh

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: '${{ secrets.GCP_CREDENTIALS_JSON }}'

      - name: ADK Tests Python ${{ matrix.python_version }}
        if: contains(fromJSON('["adk", "all"]'), env.LIBS)
        env:
          GOOGLE_CLOUD_LOCATION: global
          GOOGLE_CLOUD_PROJECT: opik-sdk-tests
          GOOGLE_GENAI_USE_VERTEXAI: TRUE
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          OPENAI_ORG_ID: ${{ secrets.OPENAI_ORG_ID }}
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        uses: ./.github/actions/install_opik_and_run_e2e_lib_integration_tests
        with:
          python_version: ${{ matrix.python_version }}
          library_name: adk

      - name: LiteLLM Tests Python ${{ matrix.python_version }}
        if: contains(fromJSON('["litellm", "all"]'), env.LIBS)
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          OPENAI_ORG_ID:  ${{ secrets.OPENAI_ORG_ID }}
        uses: ./.github/actions/install_opik_and_run_e2e_lib_integration_tests
        with:
          python_version: ${{ matrix.python_version }}
          library_name: litellm

      - name: Stop opik server
        run: |
          cd ${{ github.workspace }}/deployment/docker-compose
          docker compose down
