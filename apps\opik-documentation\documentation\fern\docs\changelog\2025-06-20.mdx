## 🔌 Integrations and SDK
- Added **CloudFlare's WorkersAI** integration ([docs](https://www.comet.com/docs/opik/reference/typescript-sdk/integrations/workers-ai))
- **Google ADK** integration: tracing is now automatically propagated to all sub-agents in agentic systems with the new `track_adk_agent_recursive` feature, eliminating the need to manually add tracing to each sub-agent.
- **Google ADK** integration: now we retrieve session-level information from the ADK framework to enrich the threads data.
- **New in the SDK!** Real-time tracking for long-running spans/traces is now supported. When enabled (set `os.environ["OPIK_LOG_START_TRACE_SPAN"] = "True"` in your environment), you can see traces and spans update live in the UI—even for jobs that are still running. This makes debugging and monitoring long-running agents much more responsive and convenient.

## 🧵 Threads improvements
- Added **Token Count and Cost Metrics** in Thread table
- Added **Sorting on all Thread table columns**
- Added **Navigation** from Thread Detail to all related traces
- Added support for **"pretty mode"** in OpenAI Agents threads

## 🧪 Experiments improvements
- Added support for filtering by **configuration metadata** to experiments. It is now also possible to add a new column displaying the configuration in the experiments table.

## 🛠 Agent Optimizer improvements
- New Public API for Agent Optimization
- Added optimization run display link
- Added `optimization_context`

## 🛡️ Security Fixes
- Fixed: h11 accepted some malformed Chunked-Encoding bodies
- Fixed: setuptools had a path traversal vulnerability in PackageIndex.download that could lead to Arbitrary File Write
- Fixed: LiteLLM had an Improper Authorization Vulnerability

👉 [See full commit log on GitHub](https://github.com/comet-ml/opik/compare/1.7.31...1.7.36)

_Releases_: `1.7.32`, `1.7.33`, `1.7.34`, `1.7.35`, `1.7.36`
