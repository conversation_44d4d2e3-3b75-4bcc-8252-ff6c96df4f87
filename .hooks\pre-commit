#!/bin/sh

# Check for staged Java files
if git diff --cached --name-only | grep -E '\.java$'; then
  echo "Java files have been changed. Running Spotless..."

  # Check if the mvn command exists
  if ! command -v mvn >/dev/null 2>&1; then
    echo "Maven (mvn) command not found. Please install <PERSON><PERSON> to use this pre-commit hook."
    exit 1
  fi

  cd apps/opik-backend || exit 1

  # Run Spotless apply
  if ! mvn spotless:check; then
    echo "Spotless found issues and failed to apply fixes. Please fix the issues before committing."
    exit 1
  fi

  # Add any potentially modified files by <PERSON><PERSON> back to the index
  git add -u
else
  echo "No Java files changed. Skipping Spotless."
fi

