name: Bug Report
description: File a bug report.
title: "[Bug]: "
labels: ["bug", "triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thank you for submitting a bug report.
        #### To help us resolve your issue, please provide fill in this bug report template.
  - type: checkboxes
    attributes:
      label: What component(s) are affected?
      options:
        - label: Opik Python SDK
          required: false
        - label: Opik Typescript SDK
          required: false
        - label: Opik Agent Optimizer SDK
          required: false
        - label: Opik UI
          required: false
        - label: Opik Server
          required: false
        - label: Documentation
          required: false
    validations:
      required: false
  - type: textarea
    validations:
      required: true
    attributes:
      label: Opik version
      placeholder: The Opik version, you can find the Opik version by running `opik.__version__` in Python.
      value: |
        - Opik version: x.x.x
  - type: textarea
    attributes:
      label: Describe the problem
      description: |
        Describe the problem clearly here, you should include both the expected behavior and the actual behavior.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Reproduction steps and code snippets
      description: Describe how to reproduce your bug. Please provide detailed steps and include code snippets if possible.
  - type: textarea
    attributes:
      label: Error logs or stack trace
      description: Please provide the error messages, logs, or stack trace that appear when your bug is reproduced, if possible.
  - type: textarea
    attributes:
      label: Healthcheck results
      description: Run `opik healthcheck` to perform some basic checks which might be useful for troubleshooting your issue. Paste your results as a text or a screenshot.
      placeholder: CLI output from 'opik healthcheck'...
