---
title: "Evolutionary Optimizer: Genetic Algorthims"
subtitle: "Discover optimal prompts with genetic algorithms and multi-objective optimization."
description: "Learn how to use the Evolutionary Optimizer to discover optimal prompts through genetic algorithms, with support for multi-objective optimization and LLM-driven genetic operations."
pytest_codeblocks_skip: true
---

The `EvolutionaryOptimizer` uses genetic algorithms to refine and discover effective prompts. It
iteratively evolves a population of prompts, applying selection, crossover, and mutation operations
to find prompts that maximize a given evaluation metric. This optimizer can also perform
multi-objective optimization (e.g., maximizing score while minimizing prompt length) and leverage
LLMs for more sophisticated genetic operations.

<Note>
  **When to Use This Optimizer:** `EvolutionaryOptimizer` is a great choice when you want to explore
  a very diverse range of prompt structures or when you have multiple objectives to optimize for
  (e.g., performance score and prompt length). Its strength lies in its ability to escape local
  optima and discover novel prompt solutions through its evolutionary mechanisms, especially when
  enhanced with LLM-driven genetic operators.

**Key Trade-offs:**

- Can be computationally intensive due to the need to evaluate a population of prompts over multiple
  generations.
- The effectiveness of LLM-driven operators (`enable_llm_crossover`, LLM-based mutations) comes with
  additional LLM call costs.
- Tuning genetic algorithm parameters (population size, mutation/crossover rates) might require some
  experimentation for optimal results on a specific task.
- While it can find very novel prompts, it might take longer to converge on a solution compared to
  more [directed optimizers](/agent_optimization/overview#supported-algorithms) for simpler tasks.

</Note>

<Info>
  Curious about `EvolutionaryOptimizer`? The [Optimizer & SDK FAQ](/agent_optimization/opik_optimizer/faq) provides
  answers to common questions, such as its strengths, how multi-objective optimization (`enable_moo`) works, the impact
  of parameters like `population_size` and `num_generations`, and the role of `output_style_guidance`.
</Info>

## How It Works

The `EvolutionaryOptimizer` is built upon the [DEAP](https://deap.readthedocs.io/) library for
evolutionary computation. Here's a breakdown of its core process:

1.  **Initialization**:

    - A population of candidate prompts is created. This can be based on an initial user-provided
      prompt, with variations generated by an LLM, or "fresh start" prompts based on the task description.
    - The optimizer can infer an `output_style_guidance` from the dataset or use a user-provided
      one. This guidance helps LLM-driven mutations and crossovers generate prompts that elicit
      responses in the desired style.

2.  **Evaluation**:

    - Each prompt (individual) in the population is evaluated against the provided dataset using
      the specified `metric`.
    - If multi-objective optimization (`enable_moo=True`) is active, multiple fitness values are
      calculated (e.g., primary metric score and prompt length). The default aims to maximize the
      primary score and minimize length.

3.  **Selection**:

    - Individuals are selected to become parents for the next generation.
    - For multi-objective optimization, NSGA-II selection (`tools.selNSGA2`) is used to maintain
      diversity and select individuals along the Pareto front.
    - For single-objective optimization, tournament selection (`tools.selTournament`) is typically
      used. Elitism can also preserve the best individuals.

4.  **Crossover**:

    - Selected parent prompts are combined to produce offspring.
    - **Standard Crossover**: Combines parts of parent prompts (e.g., sentence chunks or words).
    - **LLM-driven Crossover** (`enable_llm_crossover=True`): An LLM is used to intelligently blend
      two parent prompts, aiming to create superior child prompts that adhere to the
      `output_style_guidance`.

5.  **Mutation**:

    - Offspring prompts undergo random modifications to introduce new variations.
    - **Word-level Mutation**: Randomly replaces words with synonyms (via LLM), reorders words, or
      modifies phrases (via LLM).
    - **Structural Mutation**: Reorders sentences, combines adjacent sentences, or splits sentences.
    - **Semantic Mutation (LLM-driven)**: An LLM rephrases, simplifies, elaborates, restructures, or
      focuses the prompt based on various strategies, guided by the `output_style_guidance` and task
      context.
    - **Radical Innovation Mutation (LLM-driven)**: An LLM attempts to generate a significantly
      different and potentially much improved prompt.
    - **Adaptive Mutation**: The mutation rate can be dynamically adjusted based on population
      diversity and progress to escape local optima or fine-tune solutions.

6.  **Replacement & Iteration**:

    - The new generation of prompts (offspring, potentially with elites from the previous generation)
      replaces the old one.
    - The process (Evaluation, Selection, Crossover, Mutation) repeats for a specified number of
      `num_generations`.

7.  **Result**:
    - The best prompt found during the optimization (or the set of non-dominated solutions from the
      Pareto front in MOO) is returned as part of the `OptimizationResult`.

<Tip>
  Each prompt in the population is evaluated (Step 2) using your specified `metric` against the `dataset`. This fitness
  scoring is what drives the evolutionary selection process. To better understand this crucial step, refer to Opik's
  evaluation documentation: - [Evaluation Overview](/evaluation/overview) - [Evaluate
  Prompts](/evaluation/evaluate_prompt) - [Metrics Overview](/evaluation/metrics/overview)
</Tip>

## Configuration Options

### Basic Configuration

```python
from opik_optimizer import EvolutionaryOptimizer

optimizer = EvolutionaryOptimizer(
    model="openai/gpt-4",               # LLM for evaluating prompts and for LLM-driven genetic operations
    # Core Genetic Algorithm Parameters
    population_size=30,                 # Number of prompts in each generation
    num_generations=15,                 # Number of iterations the algorithm will run
    mutation_rate=0.2,                  # Probability of mutating an individual
    crossover_rate=0.8,                 # Probability of crossing over two individuals
    tournament_size=4,                  # Size of the tournament for selection (if not MOO)
    elitism_size=3,                     # Number of best individuals to carry over (if not MOO)
    # Advanced Features
    adaptive_mutation=True,             # Dynamically adjust mutation rate
    enable_moo=True,                    # Enable Multi-Objective Optimization (e.g., score vs. length)
                                        # Default MOO weights are (1.0, -1.0) for (score, length)
    enable_llm_crossover=True,          # Use LLM for crossover operations
    output_style_guidance=None,         # Optional: Specific guidance for LLM-generated prompts' output style
                                        # e.g., "Produce concise, factual, single-sentence answers."
    infer_output_style=True,            # If true and output_style_guidance is not set, infer from dataset
    # Technical Parameters
    n_threads=12,                       # Threads for parallel evaluation
    seed=42,                            # Random seed for reproducibility
    # LLM parameters (passed via **model_kwargs)
    temperature=0.5,                    # Temperature for LLM calls (evaluation and reasoning)
    max_tokens=1024
)
```

### Advanced Configuration

Key parameters include:

- `model`: The primary LLM used for evaluating prompts and, by default, for LLM-driven genetic operations (mutation, crossover, population initialization).
- `population_size`, `num_generations`, `mutation_rate`, `crossover_rate`: Standard GA parameters controlling the evolutionary process.
- `enable_moo`: Set to `True` to optimize for multiple objectives. The default is score (maximize) and prompt length (minimize). Fitness weights can be customized by re-registering `creator.FitnessMulti` if needed before optimizer instantiation.
- `enable_llm_crossover`: If `True`, uses an LLM to perform crossover, which can lead to more semantically meaningful children prompts.
- `adaptive_mutation`: If `True`, the optimizer will adjust the mutation rate based on population diversity and improvement progress.
- `output_style_guidance`: A string describing the desired style of the _target LLM's output_ when using the prompts being optimized. This helps LLM-driven mutations/crossovers generate prompts that are more likely to elicit correctly styled responses.
- `infer_output_style`: If `True` and `output_style_guidance` is not explicitly provided, the optimizer will attempt to infer this style by analyzing examples from the dataset.
- `**model_kwargs`: Additional keyword arguments (e.g., `temperature`, `max_tokens`) passed to the underlying LLM calls.

## Example Usage

```python
from opik_optimizer import EvolutionaryOptimizer
from opik.evaluation.metrics import LevenshteinRatio # or any other suitable metric
from opik_optimizer import datasets, ChatPrompt

# 1. Define your evaluation dataset
dataset = datasets.tiny_test() # Replace with your actual dataset

# 2. Configure the evaluation metric
def levenshtein_ratio(dataset_item, llm_output):
    return LevenshteinRatio().score(reference=dataset_item["label"], output=llm_output)

# 3. Define your base prompt and task configuration
initial_prompt = ChatPrompt(
    project_name="evolutionary_opt_project",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "{text}"}
    ]
)

# 4. Initialize the EvolutionaryOptimizer
optimizer = EvolutionaryOptimizer(
    model="openai/gpt-4o-mini",    # Choose your LLM for evaluation and reasoning
    population_size=20,
    num_generations=10,
    mutation_rate=0.25,
    crossover_rate=0.75,
    enable_moo=True,               # Optimize for score and prompt length
    enable_llm_crossover=True,
    infer_output_style=True,
    n_threads=8,
    seed=123,
    temperature=0.4
)

# 5. Run the optimization
# n_samples controls how many dataset items are used for evaluating each prompt in each generation
optimization_result = optimizer.optimize_prompt(
    prompt=initial_prompt,
    dataset=dataset,
    metric=levenshtein_ratio,
    n_samples=50 # Use 50 samples from the dataset for evaluation
)

# 6. View the results
optimization_result.display()

# For MOO, the result.prompt is often the one with the best primary score from the Pareto front.
# You can access the full Pareto front from result.details:
if optimizer.enable_moo and "pareto_front_solutions" in optimization_result.details:
    print("\n--- Pareto Front Solutions ---")
    for sol in optimization_result.details["pareto_front_solutions"]:
        print(f"Score: {sol['score']:.4f}, Length: {sol['length']:.0f}, Prompt: '{sol['prompt'][:100]}...'")
```

## Model Support

The `EvolutionaryOptimizer` uses LiteLLM for model interactions. Therefore, it supports all models
available through LiteLLM. This includes models from OpenAI, Azure OpenAI, Anthropic, google
(Vertex AI / AI Studio), Mistral AI, Cohere, locally hosted models (e.g., via Ollama), and many
others.

Refer to the [LiteLLM documentation](https://docs.litellm.ai/docs/providers) for a complete list and
how to configure them. The `model` parameter in the constructor should be the LiteLLM model string
(e.g., `"openai/gpt-4o-mini"`, `"azure/your-deployment"`, `"gemini/gemini-1.5-pro-latest"`, `"ollama_chat/llama3"`).

For detailed instructions on how to specify different models and configure providers, please refer
to the main [LiteLLM Support for Optimizers documentation page](/agent_optimization/opik_optimizer/litellm_support).

## Best Practices

1.  **Dataset Quality**: A diverse and representative dataset is crucial for meaningful evaluation and evolution of prompts.
2.  **Metric Selection**: Choose a `metric` that accurately reflects the quality of the desired output for your specific task.
3.  **Population Size & Generations**: Larger populations and more generations can lead to better results but increase computation time and cost. Start with moderate values (e.g., population 20-50, generations 10-25) and adjust based on results and budget.
4.  **LLM-driven Operations**: `enable_llm_crossover=True` and the LLM-driven mutations can produce more creative and semantically relevant prompt variations but will increase LLM calls. Balance this with cost.
5.  **Multi-Objective Optimization (MOO)**: If `enable_moo=True`, consider the trade-off between the primary metric (e.g., accuracy) and secondary objectives (e.g., prompt length). The Pareto front will give you a set of optimal trade-off solutions.
6.  **Output Style Guidance**: Leveraging `output_style_guidance` or `infer_output_style` can significantly help the LLM-driven genetic operators to create prompts that not only perform well but also elicit responses in the correct format or style.
7.  **Seeding**: Use the `seed` parameter for reproducible runs, especially during experimentation.
8.  **`n_samples` for `optimize_prompt`**: Carefully choose `n_samples`. Evaluating every prompt in the population against the full dataset for many generations can be slow. Using a representative subset (`n_samples`) speeds up evaluation per generation.

## Research and References

Genetic algorithms and evolutionary computation are well-established fields. This optimizer draws inspiration from applying these classical techniques to the domain of prompt engineering, with enhancements using LLMs for more intelligent genetic operations. You can see some additional resources:

- [DEAP Library](https://deap.readthedocs.io/en/master/): The underlying evolutionary computation framework used.

## Next Steps

- Explore other [Optimization Algorithms](/agent_optimization/overview#optimization-algorithms)
- Explore [Dataset Requirements](/agent_optimization/opik_optimizer/datasets)
- Try the [Example Projects & Cookbooks](/agent_optimization/opik_optimizer/quickstart) for runnable Colab notebooks using this optimizer

```python
# Create an instance of the EvolutionaryOptimizer
# This assumes you have your data prepared as shown in the "Preparing Your Data" section
# and your evaluation function defined as in "Defining an Evaluation Function"
optimizer = EvolutionaryOptimizer(
    model="openai/gpt-4",
    population_size=30,
    num_generations=15,
    mutation_rate=0.2,
    crossover_rate=0.8,
    tournament_size=4,
    elitism_size=3,
    adaptive_mutation=True,
    enable_moo=True,
    enable_llm_crossover=True,
    output_style_guidance=None,
    infer_output_style=True,
    n_threads=12,
    seed=42,
    temperature=0.5,
    max_tokens=1024
)
```

### Advanced Configuration

```python
# Example of advanced configuration
advanced_optimizer = EvolutionaryOptimizer(
    config=optimizer_config,
    model="openai/gpt-4",
    population_size=30,
    num_generations=15,
    mutation_rate=0.2,
    crossover_rate=0.8,
    tournament_size=4,
    elitism_size=3,
    adaptive_mutation=True,
    enable_moo=True,
    enable_llm_crossover=True,
    output_style_guidance=None,
    infer_output_style=True,
    n_threads=12,
    seed=42,
    temperature=0.5,
    max_tokens=1024
)
```
